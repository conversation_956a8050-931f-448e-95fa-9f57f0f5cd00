module Authentication
  module Commands
    class FusionAuthCompletePasswordlessLogin
      include Dry::Monads[:result]
      include Authentication::FusionAuthHelper

      LOGGER_APP = "murmur"
      LOGGER_MODULE = "authentication.commands.fusionauth_complete_passwordless_login"

      def initialize(
        account: nil,
        fusionauth_client: nil,
        splunk_logger: Splunk::Logger.new(Rails.logger)
      )
        @fusionauth_client = fusionauth_client || build_fusionauth_client(account)
        @splunk_logger = splunk_logger
      end

      def call(code:)
        passwordless_request = build_request(code)
        response = complete_passwordless_login(passwordless_request)

        Success(format_response(response))
      rescue => exception
        handle_error(exception, code)
      end

      private

      attr_reader :fusionauth_client, :splunk_logger

      def build_request(code)
        {code: code}
      end

      def complete_passwordless_login(request)
        response = fusionauth_client.complete_passwordless_login(request)

        validate_response!(response)
        response.success_response
      end

      def validate_response!(response)
        return if response.was_successful

        Rails.logger.error("Complete Passwordless Login Error response: #{response.error_response}, status: #{response.status}")
        raise Authentication::FusionAuthException.new(response.error_response, response.status)
      end

      def format_response(success_response)
        {
          jwt: success_response.token,
          refresh_token: success_response.refreshToken
        }
      end

      def handle_error(exception, code)
        log_error(exception, code)
        Failure(exception)
      end

      def log_error(exception, code)
        log_event(
          code: "#{LOGGER_MODULE}.error",
          msg: "Error completing passwordless login from FusionAuth",
          error: exception.to_s,
          error_msg: exception.message,
          passwordless_code: code
        )
        Sentry.capture_exception(exception)
      end

      def log_event(code:, msg:, **params)
        splunk_logger.log(
          app: LOGGER_APP,
          module: LOGGER_MODULE,
          code: code,
          msg: msg,
          **params
        )
      end
    end
  end
end
