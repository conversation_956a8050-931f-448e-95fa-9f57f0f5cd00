RSpec.describe Reports::InsightReport::BulkExport::PowerPoint::ComparisonOptions do
  let(:survey) {
    double(:survey,
      id: "survey_id",
      automatic_comparisons_v2_enabled?: automatic_comparisons_v2,
      aggregate_id: "survey_aggregate_id",
      overall_label_config: "Overall Label",
      account: double(:account, aggregate_id: "account_id"))
  }

  let(:comparison_options) do
    [
      Reports::InsightReport::Export::PowerPoint::ComparisonOption.new(
        name: "Comparison 1",
        id: "comparison-1",
        overall_summary_id: nil,
        type: :survey
      ),
      Reports::InsightReport::Export::PowerPoint::ComparisonOption.new(
        name: "Comparison 2",
        id: "comparison-2",
        overall_summary_id: nil,
        type: :survey
      ),
      Reports::InsightReport::Export::PowerPoint::ComparisonOption.new(
        name: "Comparison 3",
        id: "comparison-3",
        overall_summary_id: nil,
        type: :survey
      )
    ]
  end

  let(:legacy_comparison_options) do
    [
      Reports::InsightReport::Export::PowerPoint::ComparisonOption.new(
        id: "comparison-1",
        overall_summary_id: "comparison-1",
        name: "comparison 1",
        type: :survey
      ),
      Reports::InsightReport::Export::PowerPoint::ComparisonOption.new(
        id: "comparison-2",
        overall_summary_id: "comparison-2",
        name: "comparison 2",
        type: :survey
      )
    ]
  end

  let(:powerpoint_comparison_options) do
    ->(survey_id:, summaries:) {
      comparison_options
    }
  end

  let(:legacy_comparisons) do
    [
      Summary.new(id: "comparison-1", name: "comparison 1", summary_type: :survey),
      Summary.new(id: "comparison-2", name: "comparison 2", summary_type: :survey)
    ]
  end

  let(:comparisons) { ->(survey:) { legacy_comparisons } }

  def set_mock_has_access_to_multi_demographic_report_exports(value)
    allow_any_instance_of(ReportSharing::Queries::MultiDemographicFeatureFlag)
      .to receive(:has_access_to_multi_demographic_report_export?)
      .and_return(value)
  end

  subject {
    described_class.new(
      powerpoint_comparison_options: powerpoint_comparison_options,
      comparisons: comparisons
    ).call(survey: survey)
  }

  context "when automatic_comparisons_v2 is disabled" do
    let(:automatic_comparisons_v2) { false }

    it "returns legacy comparisons as options" do
      expect(subject).to eq(legacy_comparison_options)
    end
  end

  context "when automatic_comparisons_v2 is enabled" do
    let(:automatic_comparisons_v2) { true }

    it "returns powerpoint comparison options" do
      expect(subject).to eq(comparison_options)
    end
  end

  describe "feature flag behaviour" do
    subject do
      allow(Summary).to receive(:overall_for_survey).with(survey).and_return(
        double("SummaryCollection", in: legacy_comparisons)
      )

      described_class.new(
        powerpoint_comparison_options: powerpoint_comparison_options
      ).call(survey: survey)
    end

    context "when automatic_comparisons_v2 is disabled" do
      let(:automatic_comparisons_v2) { false }

      context "and feature flag OFF" do
        before { set_mock_has_access_to_multi_demographic_report_exports(false) }

        it "returns legacy comparison options AND calls the legacy API" do
          expect_any_instance_of(Powerpoint::PowerpointAvailableComparisons)
            .to receive(:available_for_anchor_legacy)
            .with(nil, false)
            .and_call_original
          expect_any_instance_of(Powerpoint::PowerpointAvailableComparisons)
            .not_to receive(:available_for_anchors)

          expect(subject).to eq(legacy_comparison_options)
        end
      end

      context "and feature flag ON" do
        before { set_mock_has_access_to_multi_demographic_report_exports(true) }

        it "returns legacy comparison options AND calls the new anchors API" do
          expect_any_instance_of(Powerpoint::PowerpointAvailableComparisons)
            .to receive(:available_for_anchors)
            .with([], false)
            .and_call_original
          expect_any_instance_of(Powerpoint::PowerpointAvailableComparisons)
            .not_to receive(:available_for_anchor_legacy)

          expect(subject).to eq(legacy_comparison_options)
        end
      end
    end

    context "when automatic_comparisons_v2 is enabled" do
      let(:automatic_comparisons_v2) { true }

      context "and feature flag OFF" do
        before { set_mock_has_access_to_multi_demographic_report_exports(false) }

        it "returns powerpoint comparison options AND calls the legacy API" do
          expect_any_instance_of(Powerpoint::PowerpointAvailableComparisons)
            .to receive(:available_for_anchor_legacy)
            .with(nil, false)
            .and_call_original
          expect_any_instance_of(Powerpoint::PowerpointAvailableComparisons)
            .not_to receive(:available_for_anchors)

          expect(subject).to eq(comparison_options)
        end
      end

      context "and feature flag ON" do
        before { set_mock_has_access_to_multi_demographic_report_exports(true) }

        it "returns powerpoint comparison options AND calls the new anchors API" do
          expect_any_instance_of(Powerpoint::PowerpointAvailableComparisons)
            .to receive(:available_for_anchors)
            .with([], false)
            .and_call_original
          expect_any_instance_of(Powerpoint::PowerpointAvailableComparisons)
            .not_to receive(:available_for_anchor_legacy)

          expect(subject).to eq(comparison_options)
        end
      end
    end
  end
end
