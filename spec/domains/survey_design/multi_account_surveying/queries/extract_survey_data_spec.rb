require "rails_helper"

RSpec.describe SurveyDesign::MultiAccountSurveying::Queries::ExtractSurveyData do
  let(:logger) { double("Splunk::Logger") }
  let(:query) { described_class.new(logger: logger) }

  let(:account) { FactoryBot.create(:account, subdomain: "test") }
  let(:survey) {
    FactoryBot.create(
      :survey,
      name: "Test Survey",
      account_id: account.id,
      description: "Test Description",
      launched_at: "2025-04-01T00:00:00Z",
      status: :design,
      type: :engagement,
      survey_period_type: :snapshot
    )
  }

  before do
    allow(logger).to receive(:log)
    survey.assign_config!(Configs::CAPTURE_LIKERT_COLOR_SCHEMA, "classical")
    survey.assign_config!(Configs::SUPPORTED_LOCALES, ["en"])
    survey.assign_flag!(Flags::IMPROVED_COMMS_CONFIGURATION, "enabled")
  end

  describe "#call" do
    context "when the survey is valid and active" do
      it "returns the survey data" do
        result = query.call(survey_aggregate_id: survey.aggregate_id)
        expect(result).to be_success
        expect(result.value!).to eq(
          id: survey.aggregate_id,
          mongoId: survey.id.to_s,
          configuration: {
            "captureLikertColorSchema" => "classical",
            "supportedLocales" => ["en"]
          },
          flags: {
            "improvedCommsConfiguration" => "enabled"
          },
          nameTranslations: {"en" => "Test Survey"},
          description: {"en" => "Test Description"},
          launchedAt: "2025-04-01T00:00:00Z",
          status: "design",
          type: "engagement",
          surveyPeriodType: "snapshot",
          isOrgSurvey: false
        )
      end
    end

    context "when the survey is closed or archived" do
      let(:archived_survey) { FactoryBot.create(:survey, account_id: account.id, archived: true) }

      it "raises an InactiveSurveyError for an archived survey" do
        result = query.call(survey_aggregate_id: archived_survey.aggregate_id)
        expect(result).to be_failure
        expect(result.failure[:status]).to eq(:bad_request)
        expect(result.failure[:message]).to eq("Survey with id '#{archived_survey.aggregate_id}' is not active")
      end
    end

    context "when a translation is nil" do
      before do
        survey.description_translations = {"en" => "Survey Description", "fr" => nil}
        survey.save!
      end

      it "filters out the nil translation" do
        result = query.call(survey_aggregate_id: survey.aggregate_id)
        expect(result).to be_success
        expect(result.value![:description]).to eq({"en" => "Survey Description"})
      end
    end
  end
end
