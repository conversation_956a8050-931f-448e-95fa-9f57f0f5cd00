module SurveyDesign
  module MultiAccountSurveying
    class UnknownQuestionTypeError < StandardError; end
    class UnknownSectionTypeError < StandardError; end
    class MapperUtils
      def self.question_intended_purpose_for(type)
        case type
        when :segment
          Domains::Enums::SurveyQuestionIntendedPurposes::DEMOGRAPHIC
        when :culture
          Domains::Enums::SurveyQuestionIntendedPurposes::FEEDBACK
        when :classify
          Domains::Enums::SurveyQuestionIntendedPurposes::CLASSIFICATION
        when :reviewer_notes
          Domains::Enums::SurveyQuestionIntendedPurposes::REVIEWER_NOTES
        when :interview
          Domains::Enums::SurveyQuestionIntendedPurposes::INTERVIEW
        when :outcome
          Domains::Enums::SurveyQuestionIntendedPurposes::OUTCOME
        else
          raise UnknownQuestionTypeError, "Unable to find intended purpose for type: #{type}"
        end
      end

      def self.section_intended_purpose_for(type)
        case type
        when :segment
          Domains::Enums::SectionIntendedPurposes::DEMOGRAPHIC
        when :culture
          Domains::Enums::SectionIntendedPurposes::STANDARD
        when :interview
          Domains::Enums::SectionIntendedPurposes::INTERVIEW
        else
          raise UnknownSectionTypeError, "Unable to find intended purpose for type: #{type}"
        end
      end

      def self.map_translations(translations)
        translations
          .reject { |locale, text| locale.nil? || text.nil? }
          .map { |locale, text| {text: text, locale: locale} }
      end
    end
  end
end
