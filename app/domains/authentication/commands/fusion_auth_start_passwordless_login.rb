module Authentication
  module Commands
    class FusionAuthStartPasswordlessLogin
      include Dry::Monads[:result]
      include Authentication::FusionAuthHelper

      LOGGER_APP = "murmur"
      LOGGER_MODULE = "authentication.commands.fusionauth_start_passwordless_login"

      def initialize(
        account: nil,
        fusionauth_client: nil,
        splunk_logger: Splunk::Logger.new(Rails.logger)
      )
        @fusionauth_client = fusionauth_client || build_fusionauth_client(account)
        @splunk_logger = splunk_logger
      end

      def call(email:, application_id:)
        passwordless_request = build_request(email, application_id)
        response = start_passwordless_login(passwordless_request)

        Success(response)
      rescue => exception
        handle_error(exception, email, application_id)
      end

      private

      attr_reader :fusionauth_client, :splunk_logger

      def build_request(email, application_id)
        {
          applicationId: application_id,
          loginId: email
        }
      end

      def start_passwordless_login(request)
        response = fusionauth_client.start_passwordless_login(request)

        validate_response!(response)
        response.success_response.code
      end

      def validate_response!(response)
        return if response.was_successful

        Rails.logger.error("Start Passwordless Login Error response: #{response.error_response}, status: #{response.status}")
        raise Authentication::FusionAuthException.new(response.error_response, response.status)
      end

      def handle_error(exception, email, application_id)
        log_error(exception, email, application_id)
        Failure(exception)
      end

      def log_error(exception, email, application_id)
        log_event(
          code: "#{LOGGER_MODULE}.error",
          msg: "Error starting passwordless login from FusionAuth",
          error: exception.to_s,
          error_msg: exception.message,
          email: email,
          application_id: application_id
        )
        Sentry.capture_exception(exception)
      end

      def log_event(code:, msg:, **params)
        splunk_logger.log(
          app: LOGGER_APP,
          module: LOGGER_MODULE,
          code: code,
          msg: msg,
          **params
        )
      end
    end
  end
end
