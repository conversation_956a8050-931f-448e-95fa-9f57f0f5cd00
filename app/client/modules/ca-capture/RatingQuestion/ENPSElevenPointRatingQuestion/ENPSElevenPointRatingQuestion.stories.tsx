import { Meta, StoryObj, within, userEvent, expect } from 'ca-storybook';
import { ComponentProps } from 'react';
import { ENPSElevenPointRatingQuestion } from './ENPSElevenPointRatingQuestion';
import {
  ResponseEntrypointDecorator,
  RTLDirDecorator,
} from 'ca-storybook-decorators';

const mockDefaultUIText = {
  scaleStartLabel: 'Not at all likely',
  scaleEndLabel: 'Extremely likely',
  addComment: 'Add a comment',
  editComment: 'Edit comment',
  addCommentAria: 'Add a comment',
  editCommentAria: 'Edit comment',
  editCommentAriaDescription: 'Edit comment description',
  dialogHeader: 'Comment',
  submitLabel: 'Submit',
  submittingLabel: 'Submitting...',
  dismissLabel: 'Dismiss',
  repliesInfo: '',
  savingError: 'Error saving',
  mandatoryError: 'This question is mandatory',
  dismissWarningTitle: 'Warning',
  dismissWarningBody: 'Are you sure you want to dismiss?',
  dismissWarningConfirm: 'Yes',
  dismissWarningCancel: 'No',
};

const defaultDecorators = [ResponseEntrypointDecorator];

const meta: Meta<ComponentProps<typeof ENPSElevenPointRatingQuestion>> = {
  title: 'CA-CAPTURE/Components/ENPSElevenPointRatingQuestion',
  component: ENPSElevenPointRatingQuestion,
  args: {
    questionId: '123',
    questionText: 'How likely are you to recommend Hooli to a friend?',
    questionMandatory: false,
    defaultCommentContent: '',
    saveUrl: '/save',
    localeDirection: 'ltr',
    allowComments: true,
    showAdministratorCanReplyToCommentMessage: false,
    existingAnswer: null,
    scaleColorSchema: 'classical',
    uiText: mockDefaultUIText,
  },
  parameters: {
    layout: 'fullscreen',
  },
  decorators: defaultDecorators,
};
export default meta;

type Story = StoryObj<ComponentProps<typeof ENPSElevenPointRatingQuestion>>;

export const Default: Story = {
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);
    const scaleFieldSet = canvas.getByTestId('enps-eleven-point-rating-scale');
    expect(scaleFieldSet).not.toHaveAttribute('aria-required');
  },
};

export const Mandatory: Story = {
  args: {
    questionMandatory: true,
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    const scaleFieldSet = canvas.getByTestId('enps-eleven-point-rating-scale');
    expect(scaleFieldSet).toHaveAttribute('aria-required', 'true');
  },
};

export const WithExistingAnswerZeroPoint: Story = {
  args: {
    existingAnswer: 0,
  },
};

export const WithExistingAnswer10Points: Story = {
  args: {
    existingAnswer: 10,
  },
};

export const BlueScaleWithExistingAnswer: Story = {
  args: {
    scaleColorSchema: 'blue',
    existingAnswer: 8,
  },
};

export const AddCommentModal: Story = {
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    await userEvent.click(await canvas.findByText('Add a comment'));
    // Only after the click does the modal window display (outside the storybook root).
    const bodyElement = within(
      (canvasElement?.parentNode as HTMLElement) || canvasElement
    );

    await bodyElement.findByText('Comment');
  },
};

export const AddCommentModalShowsAdminCanReplyMessage: Story = {
  args: {
    showAdministratorCanReplyToCommentMessage: true,
    uiText: {
      ...mockDefaultUIText,
      repliesInfo: 'Example "admin can reply to" comment message',
    },
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    await userEvent.click(await canvas.findByText('Add a comment'));
    // Only after the click does the modal window display (outside the storybook root).
    const bodyElement = within(
      (canvasElement?.parentNode as HTMLElement) || canvasElement
    );

    await bodyElement.findByText(
      'Example "admin can reply to" comment message'
    );
  },
};

export const WithCommentsDisabled: Story = {
  args: {
    allowComments: false,
  },
};

export const WithExistingComment: Story = {
  args: {
    defaultCommentContent: 'This is a default comment',
  },
};

export const WithExistingCommentModal: Story = {
  args: {
    defaultCommentContent: 'This is a default comment',
  },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement);

    await userEvent.click(await canvas.findByText('Edit comment'));
    // Only after the click does the modal window display (outside the storybook root).
    const bodyElement = within(
      (canvasElement?.parentNode as HTMLElement) || canvasElement
    );

    await bodyElement.findByDisplayValue('This is a default comment');
  },
};

export const RTLDirection: Story = {
  args: {
    localeDirection: 'rtl',
    questionText: 'אני מוכן להמליץ על Pied Piper כמקום עבודה מצוין',
    uiText: {
      scaleStartLabel: 'מאוד לא מסכים/ה',
      scaleEndLabel: 'מסכים/ה ביותר',
      addComment: 'הוסף תגובה',
      editComment: 'עריכת תגובה',
      addCommentAria: 'הוספת תגובה לשאלה',
      editCommentAria: 'עריכת התגובה לשאלה',
      editCommentAriaDescription: 'התגובה הנוכחית היא',
      dialogHeader: 'תגובות נוספות',
      submitLabel: 'שמירה',
      submittingLabel: 'מתבצעת שמירה...',
      dismissLabel: 'בטל',
      repliesInfo: 'מנהלים עשויים להשיב, אך זהותך תישאר חסויה.',
      savingError:
        'לא הצלחנו לשמור את השינויים שלך. נסה לרענן את הדף לפני עריכת שינויים נוספים.',
      mandatoryError: 'זוהי שאלת חובה',
      dismissWarningTitle: 'האם אתה בטוח?',
      dismissWarningBody:
        'קיימים שינויים שלא נשמרו, האם אתה בטוח שברצונך לסגור את תיבת דו-שיח זו?',
      dismissWarningConfirm: 'אישור',
      dismissWarningCancel: 'ביטול',
    },
  },
  decorators: [...defaultDecorators, RTLDirDecorator],
};

export const DefaultMobile: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'midSmall',
    },
  },
};
