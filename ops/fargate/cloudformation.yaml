---
Parameters:
  TaskMemory:
    Type: String
    Description: The amount (in mb) of ram to be reserved by the task
    Default: 4096
  TaskCpu:
    Type: Number
    Description: the number of cpu units to be reserved by the task the max value is instance.Vcpu * 1024
    Default: 2048
  LaunchType:
    Type: String
    AllowedValues: ["EC2", "FARGATE"]
    Default: "FARGATE"
  TaskCount:
    Type: Number
    Description: number of simultaneous tasks to run for the main cluster
    Default: 2
  TaskCountReporting:
    Type: Number
    Description: number of simultaneous tasks to run for reporting
    Default: 1
  Subnets:
    Type: List<AWS::EC2::Subnet::Id>
    Description: service subnets
  VpcId:
    Type: AWS::EC2::VPC::Id
    Description: vpc id for application target group
  VpnIp:
    Type: String
    Description: vpn to allow access to application
  EcrImage:
    Type: String
    Description: Docker image repo
    Default: 226140413739.dkr.ecr.us-west-2.amazonaws.com/murmur
  EcrBaseDomain:
    Type: String
    Description: Docker image repo
    Default: 226140413739.dkr.ecr.us-west-2.amazonaws.com
  ImageTag:
    Type: String
    Description: Docker image tag - eg. "build-123"
  Cluster:
    Type: String
    Description: The cluster name to use
  Environment:
    Type: String
    Description: Name of the Environment
    AllowedValues:
      - "development"
      - "staging"
      - "production"

  FullEnvironment:
    Description: The full description the environment, like production-eu
    Type: String

  FarmName:
    Type: String
    Description: Name of the Farm
  ParameterStoreKmsKeyArn:
    Type: String
    Description: parameter store kms key arn
  MurmurEventStoreParameterStoreWildcard:
    Type: String
    Description: Arn of the parameters used to configure the Mumur event store
  NginxConfig:
    Type: String
    Description: The base64 encoded string that contains the nginx config, set via the create-stack and create-chanegset scripts
  CertificateArn:
    Type: String
    Description: Arn of the certificate used in the ALB
  S3EmployeeDataBucketRoleArn:
    Type: String
    Description: ARN for the S3 Employee Data Bucket Role, Also used for the AWS_S3_EMPLOYEE_DATA_BUCKET_ROLE_ARN Env value

  S3DataExportRoleArn:
    Description: The Role Arn to allow access to the S3 export bucket. Also used for the AWS_S3_DATA_EXPORT_ROLE_ARN Env value
    Type: String

  SplunkforwarderConfig:
    Type: String
    Description: |
      Comma seperated list of splunk forwarder monitor configs, see https://github.com/splunk/docker-splunk/blob/fbfe867fa88eb89d8589d799b76db49f1863d8cd/documentation/ADVANCED.md#environment
      e.g. "monitor /logs/lograge.log -index main -sourcetype lograge-json,monitor /logs/oink.log -index main -sourcetype murmur-oink"
  SplunkIndexerAddress:
    Type: String
    Description: The URL of the splunk server indexer, e.g. index.splunk.development.cultureamp.net:9997
  SplunkLogPath:
    Type: String
    Description: The path that the logs are located at in the splunk container, this should match the legacy server log location so they show up correctly in splunk
    Default: /home/<USER>/current/log

  # Deployment
  ServiceDeployMinPercent:
    Description: Minimum as percent of DesiredCount that must run and be healthy during deployment
    Type: Number
    Default: 100

  ServiceDeployMaxPercent:
    Description: Maximum as percent of DesiredCount that can run during deployment
    Type: Number
    Default: 200

  # Env Values for murmur task
  EnvRailsEnv:
    Description: Environment variable value for RAILS_ENV
    Type: String

  EnvConfigName:
    Description: The name of the file under ./config/dotenv/<rails_env>/ to load ENV config from
    Type: String

  HiddenKey:
    Type : 'AWS::SSM::Parameter::Value<String>'
    Description: Key for hidden pages like the health check
    Default: /murmur/env/HIDDEN_KEY

  # env values for enabling ecs autoscaling
  EnableAutoscaling:
    Description: Set value for turning on auto scaling for ecs
    Default: "false"
    Type: String
    AllowedValues: ["true", "false"]
    ConstraintDescription: "must specify true or false"

  TargetLatency:
    Type: Number
    Default: 3
    Description: Target Latency for requests in seconds

  ScaleInCooldown:
    Type: Number
    Default: 300
    Description: |
      The amount of time, in seconds, after a scale-in activity completes
      before another scale-in activity can start.

  ScaleOutCooldown:
    Type: Number
    Default: 150
    Description: The amount of time, in seconds, to wait for a previous scale-out activity to take effect.

  MaxCapacity:
    Type: Number
    Default: 4
    Description: The max task count

  MinCapacity:
    Type: Number
    Default: 1
    Description: The min task count

  TargetLatencyReporting:
    Type: Number
    Default: 50
    Description: Target Latency for requests in seconds

  MaxCapacityReporting:
    Type: Number
    Default: 4
    Description: The max task count

  MinCapacityReporting:
    Type: Number
    Default: 1
    Description: The max task count


  EnableHrisIntegrationApi:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    Description: Whether to add a policy to allow murmur to call HRIS Integration.
    ConstraintDescription: "must specify true or false"

  EnableFusionAuthIntegration:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    ConstraintDescription: "must specify true or false"
    Description: "Toggles the availability of the FusionAuth integration secret"

  EnableLaunchDarklyIntegration:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    ConstraintDescription: "must specify true or false"
    Description: "Toggles the availability of the Launch Darkly integration secret"

  EnableInfluxBffGateway:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    ConstraintDescription: "must specify true or false"
    Description: "Toggles whether Influx BFF Gateway exists in the environment"

  EnableOrgBuilderService:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    ConstraintDescription: "must specify true or false"
    Description: "Toggles whether the org-builder-service exists in the environment"

  EnableEmployeeDataApi:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    ConstraintDescription: "must specify true or false"
    Description: "Toggles whether the employee-data-api exists in the environment"

  Updating:
    Type: String
    Default: "false"
    AllowedValues: ["true", "false"]
    ConstraintDescription: "must specify true or false"

Conditions:
  CreateEcsAutoScaling: !Equals [!Ref EnableAutoscaling, true]
  DatadogTracingEnabled: !Equals [!Ref Environment, "production"]
  InvokeHrisIntegrationApi: !Equals [!Ref EnableHrisIntegrationApi, "true"]
  FusionAuthIntegrationEnabled: !Equals [!Ref EnableFusionAuthIntegration, "true"]
  LaunchDarklyIntegrationEnabled: !Equals [!Ref EnableLaunchDarklyIntegration, "true"]
  InfluxBffGatewayPresent: !Equals [!Ref EnableInfluxBffGateway, "true"]
  OrgBuilderServicePresent: !Equals [!Ref EnableOrgBuilderService, "true"]
  EmployeeDataApiPresent: !Equals [!Ref EnableEmployeeDataApi, "true"]
  DesiredCountZero: !Equals [!Ref Updating, "true"]


Resources:
  TaskDefinition:
    Type: AWS::ECS::TaskDefinition
    UpdateReplacePolicy: Retain
    Properties:
      Family: !Ref AWS::StackName
      RequiresCompatibilities: [!Ref LaunchType]
      Cpu: !Ref TaskCpu
      Memory: !Ref TaskMemory
      ExecutionRoleArn: !GetAtt TaskExecutionRole.Arn
      TaskRoleArn: !GetAtt ParameterTaskRole.Arn
      NetworkMode: awsvpc
      Volumes:
        - Name: log-volume
        - Name: unicorn-socket
        - Name: rails-public
      ContainerDefinitions:
        - Name: murmur
          Image: !Sub "${EcrImage}:${ImageTag}"
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref TaskLogGroup
              awslogs-region: !Ref AWS::Region
              awslogs-stream-prefix: web
          Environment:
            - Name: RAILS_ENV
              Value: !Ref EnvRailsEnv
            - Name: PARAMETER_STORE_EXEC_PATH
              Value: !Sub "/${FarmName}/murmur/env/"
            - Name: FARM_NAME
              Value: !Ref FarmName
            - Name: CONFIG_NAME
              Value: !Ref EnvConfigName
            - Name: AWS_REGION
              Value: !Ref AWS::Region
            - Name: PROFILE_IMAGES_BUCKET_NAME
              Value: !ImportValue murmur-s3-buckets:ProfileImagesBucketName
            - Name: SURVEY_LOGOS_BUCKET_NAME
              Value: !ImportValue murmur-s3-buckets:SurveyLogosBucketName
            - Name: REPORT_SHARING_PERMISSIONS_FILES_BUCKET_NAME
              Value: !ImportValue murmur-s3-buckets:ReportSharingPermissionsFilesBucketName
            - Name: SENT_EMAIL_DOCUMENTS_BUCKET_2_NAME
              Value: !ImportValue murmur-s3-buckets:SentEmailDocumentsBucket2Name
            - Name: SENT_EMAIL_TRANSLATION_DOCUMENTS_BUCKET_NAME
              Value: !ImportValue murmur-s3-buckets:SentEmailTranslationDocumentsBucketName
            - Name: DD_ENV
              Value: !Ref FullEnvironment
            - Name: DD_SERVICE
              Value: murmur
            - Name: DD_VERSION
              Value: !Ref ImageTag
            - Name: DD_TAGS
              Value: !Sub "farm:${FarmName}"
            - Name: DD_TRACE_ENABLED
              Value: !If
                - DatadogTracingEnabled
                - "true"
                - "false"
            - Name: "MONGODB_READPREFERENCE"
              Value: "primary"
            - Name: "MONGODB_APPNAME"
              Value: "murmur-web"
            - Name: "LOG_TO_STDOUT"
              Value: "true"
          Secrets:
            - Name: AURORA_DB_CREDENTIALS
              ValueFrom: !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/murmur/${FarmName}/rds/MasterCredentials
            - Name: FUSION_AUTH_JWKS_JSON
              ValueFrom: !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/common/fusionauth-ops/${FarmName}/jwks.json
            - Name: SMS_SERVICE_LOCKBOX_KEY
              ValueFrom: !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/murmur/${FarmName}/sms_service/sms_service_lockbox_key
            - Name: SECRET_KEY_BASE
              ValueFrom: !Sub "arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/murmur/${FarmName}/sessions/SECRET_KEY_BASE"
            - Name: MURMUR_SERVICE_KEY
              ValueFrom: !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/fusionauth-ops/consumed-by/murmur/service-key
            - !If
              - FusionAuthIntegrationEnabled
              - Name: FUSION_AUTH_API_KEY
                ValueFrom: !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/fusionauth-ops/consumed-by/murmur/${FarmName}/keys/saml-api
              - !Ref AWS::NoValue
            - !If
              - FusionAuthIntegrationEnabled
              - Name: FUSION_AUTH_PASSWORD_RESET_API_KEY
                ValueFrom: !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/fusionauth-ops/consumed-by/murmur/password-reset-api-key
              - !Ref AWS::NoValue
            - !If
              - FusionAuthIntegrationEnabled
              - Name: MURMUR_ALL_TENANTS_API_KEY
                ValueFrom: !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/fusionauth-ops/consumed-by/murmur/all-tenants-api-key
              - !Ref AWS::NoValue
            - !If
              - FusionAuthIntegrationEnabled
              - Name: FUSION_AUTH_ENTITY_API_KEY
                ValueFrom: !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/fusionauth-ops/consumed-by/murmur/entity-api-key
              - !Ref AWS::NoValue
            - !If
              - FusionAuthIntegrationEnabled
              - Name: FUSION_AUTH_APPLICATION_UPDATE_API_KEY
                ValueFrom: !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/fusionauth-ops/consumed-by/murmur/application-update-api-key
              - !Ref AWS::NoValue
            - !If
              - FusionAuthIntegrationEnabled
              - Name: FUSION_AUTH_BASIC_AUTH_RESET_PASSWORD_SECRET
                ValueFrom: !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/fusionauth-ops/consumed-by/murmur/${FarmName}/keys/basic-auth-reset-password
              - !Ref AWS::NoValue
            - !If
              - LaunchDarklyIntegrationEnabled
              - Name: LAUNCHDARKLY_CONFIGURATION
                ValueFrom: !Sub "arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/common/launchdarkly-ops/server-config/${FarmName}"
              - !Ref AWS::NoValue

          MountPoints:
            - SourceVolume: log-volume
              ContainerPath: /murmur/current/log
            - SourceVolume: unicorn-socket
              ContainerPath: /murmur/current/tmp/sockets
            - SourceVolume: rails-public
              ContainerPath: /murmur/current/public

          DockerLabels:
            "com.datadoghq.tags.env": !Ref FullEnvironment
            "com.datadoghq.tags.service": "murmur"
            "com.datadoghq.tags.version": !Ref ImageTag

        - Name: nginx
          Image: !Sub "${EcrBaseDomain}/main/nginx:1.15"
          PortMappings:
            - ContainerPort: 80
          MountPoints:
            - SourceVolume: log-volume
              ContainerPath: /log/
            - SourceVolume: unicorn-socket
              ContainerPath: /unicorn/
            - SourceVolume: rails-public
              ContainerPath: /public/
          Environment:
            - Name: "MURMUR_NGINX_CONF"
              Value: !Ref NginxConfig
          Command:
            - /bin/bash
            - -c
            - "echo $MURMUR_NGINX_CONF | base64 -d > /etc/nginx/conf.d/default.conf && while [ ! -e /unicorn/unicorn.sock ]; do echo 'waiting for socket file'; sleep 1; done && exec nginx -g 'daemon off;'"
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref TaskLogGroup
              awslogs-region: !Ref AWS::Region
              awslogs-stream-prefix: splunk

        - Name: splunk
          Image: !Sub "${EcrBaseDomain}/main/splunkforwarder:7.2"
          MountPoints:
            - SourceVolume: log-volume
              ContainerPath: !Ref SplunkLogPath
          Environment:
            - Name: SPLUNK_INDEXER_URL
              Value: !Ref SplunkIndexerAddress
            - Name: SPLUNK_ADD
              Value: !Ref SplunkforwarderConfig
            - Name: SPLUNK_START_ARGS
              Value: "--accept-license"
            - Name: SPLUNK_PASSWORD
              Value: as09sadldfghjklPOIUYT

          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref TaskLogGroup
              awslogs-region: !Ref AWS::Region
              awslogs-stream-prefix: splunk

        - Name: datadog-agent
          # NOTE: The images are currently manually updated in the `ecr-mirrors` repo
          # Please check in with SRE to prepare an updated image version if needed
          # Remove this comment once SRE have automations for the repo
          Image: !Sub "${EcrBaseDomain}/main/ecr-mirrors/datadog/agent:7.32.1"
          MemoryReservation: 256
          Cpu: 10
          Environment:
            - Name: ECS_FARGATE
              Value: "true"
            - Name: DD_APM_ENABLED
              Value: "true"
            - Name: "DD_DOCKER_LABELS_AS_TAGS"
              Value: '{"com.datadoghq.tags.service":"service_name"}'
            - Name: DD_APM_IGNORE_RESOURCES
              Value: "/healthy*"
            - Name: DD_DOGSTATSD_NON_LOCAL_TRAFFIC
              Value: "true"
          PortMappings:
            - ContainerPort: 8126
          Secrets:
            - Name: DD_API_KEY
              ValueFrom: /common/DD_API_KEY

  SubscriptionFilter:
    Type: AWS::Logs::SubscriptionFilter
    Properties:
      LogGroupName: !Ref TaskLogGroup
      FilterPattern: ""
      DestinationArn: !Sub "arn:aws:kinesis:${AWS::Region}:${AWS::AccountId}:stream/log-handler-log-stream"
      RoleArn: !Sub "arn:aws:iam::${AWS::AccountId}:role/log-handler-subscription-role"

  TaskDefinitionReporting:
    Type: AWS::ECS::TaskDefinition
    UpdateReplacePolicy: Retain
    Properties:
      Family: !Ref AWS::StackName
      RequiresCompatibilities: [!Ref LaunchType]
      Cpu: !Ref TaskCpu
      Memory: !Ref TaskMemory
      ExecutionRoleArn: !GetAtt TaskExecutionRole.Arn
      TaskRoleArn: !GetAtt ParameterTaskRole.Arn
      NetworkMode: awsvpc
      Volumes:
        - Name: log-volume
        - Name: unicorn-socket
        - Name: rails-public
      ContainerDefinitions:
        - Name: murmur
          Image: !Sub "${EcrImage}:${ImageTag}"
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref TaskLogGroup
              awslogs-region: !Ref AWS::Region
              awslogs-stream-prefix: web
          Environment:
            - Name: RAILS_ENV
              Value: !Ref EnvRailsEnv
            - Name: PARAMETER_STORE_EXEC_PATH
              Value: !Sub "/${FarmName}/murmur/env/"
            - Name: FARM_NAME
              Value: !Ref FarmName
            - Name: CONFIG_NAME
              Value: !Ref EnvConfigName
            - Name: AWS_REGION
              Value: !Ref AWS::Region
            - Name: PROFILE_IMAGES_BUCKET_NAME
              Value: !ImportValue murmur-s3-buckets:ProfileImagesBucketName
            - Name: SURVEY_LOGOS_BUCKET_NAME
              Value: !ImportValue murmur-s3-buckets:SurveyLogosBucketName
            - Name: REPORT_SHARING_PERMISSIONS_FILES_BUCKET_NAME
              Value: !ImportValue murmur-s3-buckets:ReportSharingPermissionsFilesBucketName
            - Name: SENT_EMAIL_DOCUMENTS_BUCKET_2_NAME
              Value: !ImportValue murmur-s3-buckets:SentEmailDocumentsBucket2Name
            - Name: SENT_EMAIL_TRANSLATION_DOCUMENTS_BUCKET_NAME
              Value: !ImportValue murmur-s3-buckets:SentEmailTranslationDocumentsBucketName
            - Name: DD_ENV
              Value: !Ref FullEnvironment
            - Name: DD_SERVICE
              Value: murmur
            - Name: DD_VERSION
              Value: !Ref ImageTag
            - Name: DD_TAGS
              Value: !Sub "farm:${FarmName}"
            - Name: DD_TRACE_ENABLED
              Value: !If
                - DatadogTracingEnabled
                - "true"
                - "false"
            - Name: "MONGODB_READPREFERENCE"
              Value: "secondaryPreferred"
            - Name: "MONGODB_APPNAME"
              Value: "murmur-web-reporting"
          Secrets:
            - Name: AURORA_DB_CREDENTIALS
              ValueFrom: !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/murmur/${FarmName}/rds/MasterCredentials
            - Name: FUSION_AUTH_JWKS_JSON
              ValueFrom: !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/common/fusionauth-ops/${FarmName}/jwks.json
            - Name: SECRET_KEY_BASE
              ValueFrom: !Sub "arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/murmur/${FarmName}/sessions/SECRET_KEY_BASE"
            - !If
              - FusionAuthIntegrationEnabled
              - Name: FUSION_AUTH_API_KEY
                ValueFrom: !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/fusionauth-ops/consumed-by/murmur/${FarmName}/keys/saml-api
              - !Ref AWS::NoValue
            - !If
              - FusionAuthIntegrationEnabled
              - Name: FUSION_AUTH_PASSWORD_RESET_API_KEY
                ValueFrom: !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/fusionauth-ops/consumed-by/murmur/password-reset-api-key
              - !Ref AWS::NoValue
            - !If
              - FusionAuthIntegrationEnabled
              - Name: MURMUR_ALL_TENANTS_API_KEY
                ValueFrom: !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/fusionauth-ops/consumed-by/murmur/all-tenants-api-key
              - !Ref AWS::NoValue
            - !If
              - FusionAuthIntegrationEnabled
              - Name: FUSION_AUTH_ENTITY_API_KEY
                ValueFrom: !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/fusionauth-ops/consumed-by/murmur/entity-api-key
              - !Ref AWS::NoValue
            - !If
              - FusionAuthIntegrationEnabled
              - Name: FUSION_AUTH_APPLICATION_UPDATE_API_KEY
                ValueFrom: !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/fusionauth-ops/consumed-by/murmur/application-update-api-key
              - !Ref AWS::NoValue
            - !If
              - LaunchDarklyIntegrationEnabled
              - Name: LAUNCHDARKLY_CONFIGURATION
                ValueFrom: !Sub "arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/common/launchdarkly-ops/server-config/${FarmName}"
              - !Ref AWS::NoValue

          MountPoints:
            - SourceVolume: log-volume
              ContainerPath: /murmur/current/log
            - SourceVolume: unicorn-socket
              ContainerPath: /murmur/current/tmp/sockets
            - SourceVolume: rails-public
              ContainerPath: /murmur/current/public

          DockerLabels:
            "com.datadoghq.tags.env": !Ref FullEnvironment
            "com.datadoghq.tags.service": "murmur"
            "com.datadoghq.tags.version": !Ref ImageTag

        - Name: nginx
          Image: !Sub "${EcrBaseDomain}/main/nginx:1.15"
          PortMappings:
            - ContainerPort: 80
          MountPoints:
            - SourceVolume: log-volume
              ContainerPath: /log/
            - SourceVolume: unicorn-socket
              ContainerPath: /unicorn/
            - SourceVolume: rails-public
              ContainerPath: /public/
          Environment:
            - Name: "MURMUR_NGINX_CONF"
              Value: !Ref NginxConfig
          Command:
            - /bin/bash
            - -c
            - "echo $MURMUR_NGINX_CONF | base64 -d > /etc/nginx/conf.d/default.conf && while [ ! -e /unicorn/unicorn.sock ]; do echo 'waiting for socket file'; sleep 1; done && exec nginx -g 'daemon off;'"
          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref TaskLogGroup
              awslogs-region: !Ref AWS::Region
              awslogs-stream-prefix: splunk

        - Name: splunk
          Image: !Sub "${EcrBaseDomain}/main/splunkforwarder:7.2"
          MountPoints:
            - SourceVolume: log-volume
              ContainerPath: !Ref SplunkLogPath
          Environment:
            - Name: SPLUNK_INDEXER_URL
              Value: !Ref SplunkIndexerAddress
            - Name: SPLUNK_ADD
              Value: !Ref SplunkforwarderConfig
            - Name: SPLUNK_START_ARGS
              Value: "--accept-license"
            - Name: SPLUNK_PASSWORD
              Value: as09sadldfghjklPOIUYT

          LogConfiguration:
            LogDriver: awslogs
            Options:
              awslogs-group: !Ref TaskLogGroup
              awslogs-region: !Ref AWS::Region
              awslogs-stream-prefix: splunk

        - Name: datadog-agent
          # NOTE: The images are currently manually updated in the `ecr-mirrors` repo
          # Please check in with SRE to prepare an updated image version if needed
          # Remove this comment once SRE have automations for the repo
          Image: !Sub "${EcrBaseDomain}/main/ecr-mirrors/datadog/agent:7.32.1"
          MemoryReservation: 256
          Cpu: 10
          Environment:
            - Name: ECS_FARGATE
              Value: "true"
            - Name: DD_APM_ENABLED
              Value: "true"
            - Name: "DD_DOCKER_LABELS_AS_TAGS"
              Value: '{"com.datadoghq.tags.service":"service_name"}'
            - Name: DD_APM_IGNORE_RESOURCES
              Value: "/healthy*"
            - Name: DD_DOGSTATSD_NON_LOCAL_TRAFFIC
              Value: "true"
          PortMappings:
            - ContainerPort: 8126
          Secrets:
            - Name: DD_API_KEY
              ValueFrom: /common/DD_API_KEY



  TaskLogGroup:
    Type: AWS::Logs::LogGroup
    Properties:
      LogGroupName: !Sub ${AWS::StackName}
      RetentionInDays: 60

  EcsService:
    Type: AWS::ECS::Service
    Metadata:
      cfn-lint:
        config:
          ignore_checks:
          - E3002
    DependsOn:
      - ApplicationListener443
    Properties:
      DeploymentConfiguration:
        MaximumPercent: !Ref ServiceDeployMaxPercent
        MinimumHealthyPercent: !Ref ServiceDeployMinPercent
      Cluster: !Ref Cluster
      DesiredCount: !If [DesiredCountZero, !Ref 'AWS::NoValue', !Ref TaskCount]
      HealthCheckGracePeriodSeconds: 60
      TaskDefinition: !Ref TaskDefinition
      LaunchType: !Ref LaunchType
      PlatformVersion: 1.3.0
      PropagateTags: SERVICE
      LoadBalancers:
        - ContainerName: nginx
          ContainerPort: 80
          TargetGroupArn: !Ref ApplicationTargetGroup
      NetworkConfiguration:
        AwsvpcConfiguration:
          Subnets: !Ref Subnets
          SecurityGroups:
            - !Ref TaskSecurityGroup
            - !ImportValue addis-source-security-groups:MurmurEventStoreDatabase
            - !ImportValue addis-source-security-groups:MurmurDatabase
            - !ImportValue addis-source-security-groups:MurmurOutbound
            - !ImportValue cdk-addis-source-security-groups:PerformanceEmployeeEventReader

  EcsReportingService:
    Type: AWS::ECS::Service
    Metadata:
      cfn-lint:
        config:
          ignore_checks:
          - E3002
    DependsOn:
      - ApplicationListener443
    Properties:
      DeploymentConfiguration:
        MaximumPercent: 200
        MinimumHealthyPercent: !Ref ServiceDeployMinPercent
      Cluster: !Ref Cluster
      DesiredCount: !If [DesiredCountZero, !Ref 'AWS::NoValue', !Ref TaskCountReporting]
      HealthCheckGracePeriodSeconds: 60
      TaskDefinition: !Ref TaskDefinitionReporting
      LaunchType: !Ref LaunchType
      PlatformVersion: 1.3.0
      PropagateTags: SERVICE
      LoadBalancers:
        - ContainerName: nginx
          ContainerPort: 80
          TargetGroupArn: !Ref ApplicationTargetGroupReporting
      NetworkConfiguration:
        AwsvpcConfiguration:
          Subnets: !Ref Subnets
          SecurityGroups:
            - !Ref TaskSecurityGroup
            - !ImportValue addis-source-security-groups:MurmurEventStoreDatabase
            - !ImportValue addis-source-security-groups:MurmurDatabase
            - !ImportValue addis-source-security-groups:MurmurOutbound
            - !ImportValue cdk-addis-source-security-groups:PerformanceEmployeeEventReader
      Tags:
        - Key: readonly
          Value: "true"

  TaskExecutionRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: 2012-10-17
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - ecs-tasks.amazonaws.com
            Action:
              - sts:AssumeRole
      Policies:
        - PolicyName: !Sub ${AWS::StackName}-ecr-and-logs-policy
          PolicyDocument:
            Version: 2012-10-17
            Statement:
              - Effect: Allow
                Action:
                  - ecr:GetAuthorizationToken
                  - ecr:BatchCheckLayerAvailability
                  - ecr:GetDownloadUrlForLayer
                  - ecr:BatchGetImage
                  - logs:CreateLogStream
                  - logs:PutLogEvents
                Resource: "*"
        - PolicyName: CommonKeys
          PolicyDocument:
            Version: 2012-10-17
            Statement:
              - Effect: Allow
                Action: ssm:GetParameter*
                Resource: !Sub arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/common/*
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                  - secretsmanager:DescribeSecret
                Resource: !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/common/*
        - PolicyName: FusionAuthOpsSecrets
          PolicyDocument:
            Version: 2012-10-17
            Statement:
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                  - secretsmanager:DescribeSecret
                Resource: !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/fusionauth-ops/consumed-by/murmur/*
        - PolicyName: MurmurSecrets
          PolicyDocument:
            Version: 2012-10-17
            Statement:
              - Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                  - secretsmanager:DescribeSecret
                Resource: !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/murmur/${FarmName}/*

  ParameterTaskRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Statement:
        - Effect: Allow
          Principal:
            Service:
              - ecs-tasks.amazonaws.com
          Action: sts:AssumeRole
      Policies:
        - PolicyName: access-to-parameter-store
          PolicyDocument:
            Version: 2012-10-17
            Statement:
            - Action: kms:Decrypt
              Effect: Allow
              Resource: !Ref ParameterStoreKmsKeyArn
            - Action: ssm:GetParametersByPath
              Effect: Allow
              Resource: !Sub "arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${FarmName}/murmur/*"
            - Action:
                - ssm:GetParametersByPath
                - ssm:GetParameter
              Effect: Allow
              Resource:
                - !Ref MurmurEventStoreParameterStoreWildcard
        - PolicyName: s3-bucket-access
          PolicyDocument:
            Version: 2012-10-17
            Statement:
            - Action: sts:AssumeRole
              Effect: Allow
              Resource:
                - !Ref S3DataExportRoleArn
                - !Ref S3EmployeeDataBucketRoleArn
            - Action: s3:GetObject
              Effect: Allow
              Resource:
                - !ImportValue murmur-s3-buckets:SamlBucketArn
                - !ImportValue murmur-s3-buckets:ReportSharingPermissionsFilesBucketArn
                - !ImportValue murmur-s3-buckets:SentEmailDocumentsBucket2Arn
                - !ImportValue murmur-s3-buckets:SentEmailTranslationDocumentsBucketArn
                - Fn::Sub:
                  - "${SamlBucketArn}/*"
                  - SamlBucketArn:
                      !ImportValue murmur-s3-buckets:SamlBucketArn
                - Fn::Sub:
                  - "${ReportSharingPermissionsFilesBucketArn}/*"
                  - ReportSharingPermissionsFilesBucketArn:
                      !ImportValue murmur-s3-buckets:ReportSharingPermissionsFilesBucketArn
                - Fn::Sub:
                  - "${SentEmailDocumentsBucket2Arn}/*"
                  - SentEmailDocumentsBucket2Arn:
                      !ImportValue murmur-s3-buckets:SentEmailDocumentsBucket2Arn
                - Fn::Sub:
                  - "${SentEmailTranslationDocumentsBucketArn}/*"
                  - SentEmailTranslationDocumentsBucketArn:
                      !ImportValue murmur-s3-buckets:SentEmailTranslationDocumentsBucketArn
            - Action: s3:DeleteObject
              Effect: Allow
              Resource:
                - !ImportValue murmur-s3-buckets:ReportSharingPermissionsFilesBucketArn
                - Fn::Sub:
                  - "${ReportSharingPermissionsFilesBucketArn}/*"
                  - ReportSharingPermissionsFilesBucketArn:
                      !ImportValue murmur-s3-buckets:ReportSharingPermissionsFilesBucketArn
            - Action:
              - s3:ListBucket
              Effect: Allow
              Resource:
              - !ImportValue murmur-s3-buckets:ReportSharingPermissionsFilesBucketArn
              - !ImportValue murmur-s3-buckets:SentEmailDocumentsBucket2Arn
              - !ImportValue murmur-s3-buckets:SentEmailTranslationDocumentsBucketArn
            - Action:
              - s3:PutObject
              - s3:PutObjectAcl
              Effect: Allow
              Resource:
              - Fn::Sub:
                - "${ReportSharingPermissionsFilesBucketArn}/*"
                - ReportSharingPermissionsFilesBucketArn:
                    !ImportValue murmur-s3-buckets:ReportSharingPermissionsFilesBucketArn
              - Fn::Sub:
                  - "${SentEmailDocumentsBucket2Arn}/*"
                  - SentEmailDocumentsBucket2Arn:
                      !ImportValue murmur-s3-buckets:SentEmailDocumentsBucket2Arn
              - Fn::Sub:
                  - "${SentEmailTranslationDocumentsBucketArn}/*"
                  - SentEmailTranslationDocumentsBucketArn:
                      !ImportValue murmur-s3-buckets:SentEmailTranslationDocumentsBucketArn
            - Action: kms:Decrypt
              Effect: Allow
              Resource:
                - !ImportValue murmur-s3-buckets:SamlBucketKeyArn
        - PolicyName: profile-images-s3-access
          PolicyDocument:
            Version: 2012-10-17
            Statement:
            - Action:
                - s3:ListBucket
              Effect: Allow
              Resource:
                - !ImportValue murmur-s3-buckets:ProfileImagesBucketArn
                - !ImportValue murmur-s3-buckets:SurveyLogosBucketArn
            - Action:
                - s3:GetObject
                - s3:PutObject
                - s3:PutObjectAcl
                - s3:DeleteObject
              Effect: Allow
              Resource:
                - !ImportValue murmur-s3-buckets:ProfileImagesBucketArn
                - !ImportValue murmur-s3-buckets:SurveyLogosBucketArn
                - Fn::Sub:
                  - "${ProfileImagesBucketArn}/*"
                  - ProfileImagesBucketArn:
                      !ImportValue murmur-s3-buckets:ProfileImagesBucketArn
                - Fn::Sub:
                    - "${SurveyLogosBucketArn}/*"
                    - SurveyLogosBucketArn:
                        !ImportValue murmur-s3-buckets:SurveyLogosBucketArn
        - PolicyName: launchdarkly-dynamodb-access
          PolicyDocument:
            Version: 2012-10-17
            Statement:
            - Action: dynamodb:GetItem
              Effect: Allow
              Resource: "arn:aws:dynamodb:*:*:table/launchdarkly-ops-relay-data-*"
        - !If
            - InvokeHrisIntegrationApi
            -
              PolicyName: invoke-hris-integration
              PolicyDocument:
                Version: 2012-10-17
                Statement:
                - Action: execute-api:Invoke
                  Effect: Allow
                  Resource:
                    - Fn::Sub:
                        - "arn:aws:execute-api:${AWS::Region}:${AWS::AccountId}:${HrisIntegrationApiGwId}/*/*/*"
                        - HrisIntegrationApiGwId:
                            !ImportValue
                            'Fn::Sub': 'hris-integration-${FarmName}-ApiGwId'
            - !Ref "AWS::NoValue"
  TaskSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: !Sub ${AWS::StackName}-sg
      GroupDescription: allow access from ALB
      VpcId: !Ref VpcId
      SecurityGroupIngress:
        - ToPort: 80
          FromPort: 80
          IpProtocol: tcp
          SourceSecurityGroupId: !Ref ALBSecurityGroup
      Tags:
        - Key: Name
          Value: !Sub ${AWS::StackName}-sg

  ALBSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupName: !Sub ${AWS::StackName}-alb-sg
      GroupDescription: allow access to app via vpn
      VpcId: !Ref VpcId
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          CidrIp: !Ref VpnIp
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue addis-source-security-groups:MurmurAppLoadBalancer
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:SlackCommunicationsService
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:BenchmarkThumbprint
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:PerformanceApi
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:SurveyConversationsService
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:SurveyTemplateLibrary
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:MsTeams
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:MsteamsConvMigration
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:ManagerLab
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:DevelopModule
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:CareerPathways
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:AccountsService
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:RecognitionService
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:ProgramsService
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:SurveyScores
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:SurveySummary
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:QuantReporting
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:EmployeeTasksService
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:UnifiedHomeData
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:SvcGwVpcLink
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:ContinuousReporting
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:ReportingMcpServer
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:GoalsService
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:CommentSummaryService
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:RolesService
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:CommentPublisher
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:DemoAccountsApi
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:ConversationsApi
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:CpRoleAssignmentBe
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:PerformCore
        - IpProtocol: tcp
          FromPort: 443
          ToPort: 443
          SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:SurveyDesign

        - !If
            - InfluxBffGatewayPresent
            - IpProtocol: tcp
              FromPort: 443
              ToPort: 443
              SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:InfluxBffGateway
            - !Ref "AWS::NoValue"
        - !If
            - OrgBuilderServicePresent
            - IpProtocol: tcp
              FromPort: 443
              ToPort: 443
              SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:OrgBuilderService
            - !Ref "AWS::NoValue"
        - !If
            - EmployeeDataApiPresent
            - IpProtocol: tcp
              FromPort: 443
              ToPort: 443
              SourceSecurityGroupId: !ImportValue cdk-addis-source-security-groups:EmployeeDataApi
            - !Ref "AWS::NoValue"

      Tags:
        - Key: Name
          Value: !Sub ${AWS::StackName}-alb-sg

  ApplicationLoadBalancer:
    Type: AWS::ElasticLoadBalancingV2::LoadBalancer
    Properties:
      Name: !Sub ${AWS::StackName}-alb
      Scheme: internal
      Subnets: !Ref Subnets
      SecurityGroups:
        - !Ref ALBSecurityGroup
      LoadBalancerAttributes:
        - Key: idle_timeout.timeout_seconds
          Value: "120"

  ApplicationListener443:
    Type: AWS::ElasticLoadBalancingV2::Listener
    Properties:
      Certificates:
        - CertificateArn: !Ref CertificateArn
      DefaultActions:
        - Type: forward
          TargetGroupArn: !Ref ApplicationTargetGroup
      LoadBalancerArn: !Ref ApplicationLoadBalancer
      Port: 443
      Protocol: HTTPS

  ListenerRuleReportingRead:
    Type: AWS::ElasticLoadBalancingV2::ListenerRule
    Properties:
      Actions:
        - Type: forward
          TargetGroupArn: !Ref ApplicationTargetGroupReporting
      Conditions:
        - Field: http-header
          HttpHeaderConfig:
            HttpHeaderName: Reporting
            Values:
              - "true"
        - Field: path-pattern
          PathPatternConfig:
            Values:
              - "*reports*"
        - Field: http-request-method
          HttpRequestMethodConfig:
            Values:
              - GET
      ListenerArn: !Ref ApplicationListener443
      Priority: 100

  ListenerRuleReportingGraphQLRead:
    Type: AWS::ElasticLoadBalancingV2::ListenerRule
    Properties:
      Actions:
        - Type: forward
          TargetGroupArn: !Ref ApplicationTargetGroupReporting
      Conditions:
        - Field: http-header
          HttpHeaderConfig:
            HttpHeaderName: Reporting
            Values:
              - "true"
        - Field: path-pattern
          PathPatternConfig:
            Values:
              - "*trend/api/graphql*"
      ListenerArn: !Ref ApplicationListener443
      Priority: 200

  ApplicationTargetGroup:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Name: !Sub ${AWS::StackName}-tg
      Port: 80
      Protocol: HTTP
      TargetType: ip
      VpcId: !Ref VpcId
      HealthCheckPath: !Sub /healthy.html?key=${HiddenKey}&only=rails,mongoid
      HealthCheckIntervalSeconds: 10
      HealthCheckTimeoutSeconds: 5
      HealthyThresholdCount: 3
      UnhealthyThresholdCount: 5
      TargetGroupAttributes:
        - Key: deregistration_delay.timeout_seconds
          Value: "30"

  ApplicationTargetGroupReporting:
    Type: AWS::ElasticLoadBalancingV2::TargetGroup
    Properties:
      Name: !Sub ${AWS::StackName}-rp
      Port: 80
      Protocol: HTTP
      TargetType: ip
      VpcId: !Ref VpcId
      HealthCheckPath: !Sub /healthy.html?key=${HiddenKey}&only=rails,mongoid
      HealthCheckIntervalSeconds: 10
      HealthCheckTimeoutSeconds: 5
      HealthyThresholdCount: 3
      UnhealthyThresholdCount: 5
      TargetGroupAttributes:
        - Key: deregistration_delay.timeout_seconds
          Value: "30"

  ScalingLatencyRole:
    Type: AWS::IAM::Role
    Condition: CreateEcsAutoScaling
    Properties:
      AssumeRolePolicyDocument:
        Version: 2012-10-17
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - application-autoscaling.amazonaws.com
            Action:
              - sts:AssumeRole

  ScalingRolePolicy:
    Type: AWS::IAM::Policy
    Condition: CreateEcsAutoScaling
    Properties:
      Roles:
        - !Ref ScalingLatencyRole
      PolicyName: ScalingRolePolicy
      PolicyDocument:
        Version: 2012-10-17
        Statement:
          - Effect: Allow
            Action:
              - ecs:RunTask
              - ecs:UpdateSerice
              - ecs:DescribeServices
            Resource: !Sub arn:aws:ecs:${AWS::Region}:${AWS::AccountId}:cluster/${Cluster}
          - Effect: Allow
            Action:
              - application-autoscaling:*
              - cloudwatch:GetMetricStatistics
            Resource: '*'

  ScalingTarget:
    Type: AWS::ApplicationAutoScaling::ScalableTarget
    Condition: CreateEcsAutoScaling
    Properties:
      MaxCapacity: !Ref MaxCapacity
      MinCapacity: !Ref MinCapacity
      ResourceId: !Join ['/', ['service', !Ref Cluster, !GetAtt [EcsService, Name]]]
      RoleARN: !GetAtt ScalingLatencyRole.Arn
      ServiceNamespace: ecs
      ScalableDimension: ecs:service:DesiredCount


  ServiceScalingPolicyLatency:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Condition: CreateEcsAutoScaling
    Properties:
      PolicyName: !Sub ${AWS::StackName}-target-tracking-response-time
      PolicyType: TargetTrackingScaling
      ScalingTargetId: !Ref ScalingTarget
      TargetTrackingScalingPolicyConfiguration:
        TargetValue: !Ref TargetLatency
        ScaleInCooldown: !Ref ScaleInCooldown
        ScaleOutCooldown: !Ref ScaleOutCooldown
        CustomizedMetricSpecification:
          Dimensions:
            - Name: TargetGroup
              Value: !GetAtt [ApplicationTargetGroup, TargetGroupFullName]
            - Name: LoadBalancer
              Value: !GetAtt [ApplicationLoadBalancer, LoadBalancerFullName]
          MetricName: "TargetResponseTime"
          Namespace: "AWS/ApplicationELB"
          Statistic: Average
          Unit: Seconds

  ScalingTargetReporting:
    Type: AWS::ApplicationAutoScaling::ScalableTarget
    Condition: CreateEcsAutoScaling
    Properties:
      MaxCapacity: !Ref MaxCapacityReporting
      MinCapacity: !Ref MinCapacityReporting
      ResourceId: !Join ['/', ['service', !Ref Cluster, !GetAtt [EcsReportingService, Name]]]
      RoleARN: !GetAtt ScalingLatencyRole.Arn
      ServiceNamespace: ecs
      ScalableDimension: ecs:service:DesiredCount

  ServiceScalingPolicyLatencyReporting:
    Type: AWS::ApplicationAutoScaling::ScalingPolicy
    Condition: CreateEcsAutoScaling
    Properties:
      PolicyName: !Sub ${AWS::StackName}-target-tracking-response-time-reporting
      PolicyType: TargetTrackingScaling
      ScalingTargetId: !Ref ScalingTargetReporting
      TargetTrackingScalingPolicyConfiguration:
        TargetValue: !Ref TargetLatencyReporting
        ScaleInCooldown: !Ref ScaleInCooldown
        ScaleOutCooldown: !Ref ScaleOutCooldown
        CustomizedMetricSpecification:
          Dimensions:
            - Name: TargetGroup
              Value: !GetAtt [ApplicationTargetGroupReporting, TargetGroupFullName]
            - Name: LoadBalancer
              Value: !GetAtt [ApplicationLoadBalancer, LoadBalancerFullName]
          MetricName: "TargetResponseTime"
          Namespace: "AWS/ApplicationELB"
          Statistic: Average
          Unit: Seconds

Outputs:
  ALBHostedZoneId:
    Description: "The CanonicalHostedZoneID for the ALB for use with the dns stack"
    Value: !GetAtt ApplicationLoadBalancer.CanonicalHostedZoneID
    Export:
      Name: !Sub "${AWS::StackName}:CanonicalHostedZoneID"
  ALBDNSName:
    Description: "The DNS Name for the ALB for use with the dns stack"
    Value: !GetAtt ApplicationLoadBalancer.DNSName
    Export:
      Name: !Sub "${AWS::StackName}:AlbDnsName"
  ALBListenerArn:
    Description: "The ARN of murmur's ALB listener"
    Value: !Ref ApplicationListener443
    Export:
      Name: !Sub "${AWS::StackName}:AlbListenerArn"
  ALBSecurityGroup:
    Description: "The Security Group of murmur's ALB"
    Value: !Ref ALBSecurityGroup
    Export:
      Name: !Sub "${AWS::StackName}:AlbSecurityGroup"
