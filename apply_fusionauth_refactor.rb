#!/usr/bin/env ruby

# Script to help apply the FusionAuth client refactoring pattern
# This script generates the exact changes needed for each file

CLASSES_TO_REFACTOR = [
  {
    file: "app/domains/authentication/commands/backfill_sliding_window_policy_in_fusionauth.rb",
    class_name: "BackfillSlidingWindowPolicyInFusionauth"
  },
  {
    file: "app/domains/authentication/commands/fusion_auth_passwordless_login.rb", 
    class_name: "FusionAuthPasswordlessLogin"
  },
  {
    file: "app/domains/authentication/commands/register_new_fusion_auth_user.rb",
    class_name: "RegisterNewFusionAuthUser"
  },
  {
    file: "app/domains/authentication/commands/fusion_auth_search_entity_type.rb",
    class_name: "FusionAuthSearchEntityType"
  },
  {
    file: "app/domains/authentication/commands/reset_registered_applications_in_google_idp.rb",
    class_name: "ResetRegisteredApplicationsInGoogleIDP"
  },
  {
    file: "app/domains/authentication/commands/fusion_auth_vend_jwt.rb",
    class_name: "FusionAuthVendJWT"
  },
  {
    file: "app/domains/authentication/commands/fusion_auth_sign_out.rb",
    class_name: "FusionAuthSignOut"
  },
  {
    file: "app/domains/authentication/commands/import_fusion_auth_user.rb",
    class_name: "ImportFusionAuthUser"
  },
  {
    file: "app/domains/authentication/queries/fetch_fusion_auth_blocking_actions.rb",
    class_name: "FetchFusionAuthBlockingActions"
  },
  {
    file: "app/domains/authentication/queries/fetch_fusion_auth_user_by_employee_id.rb",
    class_name: "FetchFusionAuthUserByEmployeeId"
  },
  {
    file: "app/domains/authentication/queries/fetch_fusion_auth_user_by_email.rb",
    class_name: "FetchFusionAuthUserByEmail"
  }
]

def generate_refactor_instructions(file_info)
  puts "=" * 80
  puts "FILE: #{file_info[:file]}"
  puts "CLASS: #{file_info[:class_name]}"
  puts "=" * 80
  
  puts "\n1. Add include statement after existing includes:"
  puts "   include Authentication::FusionAuthHelper"
  
  puts "\n2. Update initialize method parameters:"
  puts "   Change:"
  puts "     fusionauth_client: ::Authentication::FusionAuthClient.new,"
  puts "   To:"
  puts "     account: nil,"
  puts "     fusionauth_client: nil,"
  
  puts "\n3. Update fusionauth_client assignment:"
  puts "   Change:"
  puts "     @fusionauth_client = fusionauth_client"
  puts "   To:"
  puts "     @fusionauth_client = fusionauth_client || build_fusionauth_client(account)"
  
  puts "\n4. Update any callers to pass account parameter during initialization"
  puts "\n" + "=" * 80 + "\n"
end

puts "FusionAuth Client Refactoring Instructions"
puts "=========================================="
puts "\nThis script provides step-by-step instructions for refactoring each class."
puts "Apply these changes manually using your editor.\n"

CLASSES_TO_REFACTOR.each do |class_info|
  generate_refactor_instructions(class_info)
end

puts "\nAFTER REFACTORING ALL CLASSES:"
puts "1. Update tests to pass account parameter where needed"
puts "2. Test both feature flag ON and OFF scenarios"  
puts "3. Ensure backward compatibility when account is nil"
puts "4. Run the test suite to verify everything works"

puts "\nREFERENCE EXAMPLES:"
puts "- See Authentication::Commands::FusionAuthSamlSignIn for complete pattern"
puts "- See Authentication::Commands::FusionAuthStartPasswordlessLogin for another example"
puts "- See lib/saml/authenticator.rb for how to update callers"
