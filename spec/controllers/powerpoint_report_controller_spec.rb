require "rails_helper"

RSpec.describe PowerpointReportController do
  let(:job) { instance_double(Jobs::BuildPowerpointJob, id: 1, completed?: true, save: nil, download_url: "") }

  def set_mock_has_access_to_multi_demographic_report_exports(value)
    allow_any_instance_of(ReportSharing::Queries::MultiDemographicFeatureFlag)
      .to receive(:has_access_to_multi_demographic_report_export?)
      .and_return(value)
  end

  before do
    allow(Jobs::BuildPowerpointJob).to receive(:find).and_return(job)
    allow(Jobs::BuildPowerpointJob).to receive(:create).and_return(job)
  end

  describe "#show" do
    let(:account) { FactoryBot.create(:account_with_master) }
    let(:survey) do
      FactoryBot.create(:basic_survey, account: account)
    end
    let(:admin) { FactoryBot.create(:admin, account: account) }

    describe "default behaviour" do
      before do
        sign_in admin
        get :show, params: {survey_id: survey.id, report_id: "admin", id: 1}
      end

      it_behaves_like "an ok response"
    end
  end

  describe "#new" do
    let(:account) { FactoryBot.create(:account_with_master) }
    let(:survey) do
      FactoryBot.create(:basic_survey, account: account)
    end
    let(:admin) { FactoryBot.create(:admin, account: account) }

    describe "default behaviour" do
      before do
        sign_in admin
        get :new, params: {survey_id: survey.id, report_id: "admin"}
      end

      it_behaves_like "an ok response"
    end
  end

  describe "#create" do
    let(:account) { FactoryBot.create(:account_with_master) }
    let(:survey) do
      FactoryBot.create(:basic_survey, account: account)
    end
    let(:admin) { FactoryBot.create(:admin, account: account) }

    let(:submitted) { 2 }
    let(:score) { 1 }
    let(:total) { 3 }
    let(:participation_point) { instance_double(Reporting::Data::Participation::Point, score: score, submission_count: submitted, invitation_count: total) }
    let(:comparison_point) { instance_double(Reporting::Data::Participation::Point, score: score, submission_count: submitted, invitation_count: total) }
    let(:participation_data) do
      Reporting::Data::Participation::Result.with(
        overall_result: comparison_point,
        filtered_result: participation_point,
        segment_class_results: [],
        hierarchy_spreads: [],
        cross_spreads: []
      )
    end
    let(:participation_result) do
      Monad::Result.with(
        status: :ok,
        data: participation_data
      )
    end

    context "with passing reporting threshold checks" do
      before do
        sign_in admin
        allow_any_instance_of(SnapshotReportingEngineService).to receive(:overall_participation_result).and_return(participation_result)
        allow_any_instance_of(Reporting::Data::FilterSignificanceCheck).to receive(:contains_insignificant_filters?).and_return(false)
        survey.assign_config!(Configs::SIGNIFICANCE_POPULATION, 1)
      end

      context "with admin report" do
        before do
          post :create, params: {survey_id: survey.id, report_id: "admin", powerpoint_report: {demographic: "example_demographic"}}

          expect(Jobs::BuildPowerpointJob).to have_received(:create).with(hash_including(action_link: nil, demographic_id: "example_demographic"))
        end

        it_behaves_like "an ok response"
      end

      context "with created report" do
        let!(:report) { FactoryBot.create(:report, survey: survey) }
        before do
          survey.assign_flag!(Flags::LINK_TO_ACTION_FROM_PPT, flag_value)
          survey.update_attributes!(powerpoint_template: powerpoint_template)

          post :create, params: {survey_id: survey.id, report_id: report.id, powerpoint_report: {demographic: "example_demographic"}}
        end

        context "with action link" do
          let(:flag_value) { Flags::ENABLED }
          let(:powerpoint_template) { :engagement }
          before do
            expect(Jobs::BuildPowerpointJob).to have_received(:create).with(
              hash_including(
                action_link: %r{http.*/surveys/#{survey.id}/reports/#{report.id}/create_action\?comparison=.overall&filters=&locale=en&preview=true&utm_source=ppt&utm_type=action_link},
                demographic_id: "example_demographic"
              )
            )
          end
          it_behaves_like "an ok response"
        end

        context "with flag disabled" do
          let(:flag_value) { Flags::DISABLED }
          let(:powerpoint_template) { :engagement }
          before do
            expect(Jobs::BuildPowerpointJob).to have_received(:create).with(hash_including(action_link: nil, demographic_id: "example_demographic"))
          end
          it_behaves_like "an ok response"
        end

        context "with powerpoint_template none" do
          let(:flag_value) { Flags::ENABLED }
          let(:powerpoint_template) { :none }
          before do
            expect(Jobs::BuildPowerpointJob).to have_received(:create).with(hash_including(action_link: nil, demographic_id: "example_demographic"))
          end
          it_behaves_like "an ok response"
        end
      end
    end

    context "with failing reporting threshold checks" do
      before do
        sign_in admin
        allow_any_instance_of(SnapshotReportingEngineService).to receive(:overall_participation_result).and_return(participation_result)
        allow_any_instance_of(Reporting::Data::FilterSignificanceCheck).to receive(:contains_insignificant_filters?).and_return(true)
        survey.assign_config!(Configs::SIGNIFICANCE_POPULATION, 1)
        post :create, params: {survey_id: survey.id, report_id: "admin"}
      end

      it_behaves_like "an http not found error"
    end

    # context "for a free account" do
    #   before { skip("Test to be removed by Unify as part of https://trello.com/c/QKgiz7lp/43-clean-up-code-di-starter-kit-freemium") }

    #   let(:account) { FactoryBot.create(:account_without_users, status: :free).with_inclusion }

    #   before do
    #     sign_in admin
    #     allow_any_instance_of(SnapshotReportingEngineService).to receive(:overall_participation_result).and_return(participation_result)
    #     allow_any_instance_of(Reporting::Data::FilterSignificanceCheck).to receive(:contains_insignificant_filters?).and_return(false)
    #     survey.assign_config!(Configs::SIGNIFICANCE_POPULATION, 1)
    #   end

    #   it "uses the latest inclusion benchmark summary for comparison" do
    #     benchmark_report_1 = FactoryBot.create(:benchmark_report, type: :inclusion, name: "inclusion benchmark 2019", created_at: 1.hour.ago)
    #     benchmark_report_2 = FactoryBot.create(:benchmark_report, type: :inclusion, name: "inclusion benchmark 2018", created_at: 1.year.ago)
    #     benchmark_summary_1 = FactoryBot.create(:summary, name: "foo", survey: survey, summary_type: :benchmark, benchmark_report: benchmark_report_1)
    #     FactoryBot.create(:summary, survey: survey, name: "bar", summary_type: :benchmark, benchmark_report: benchmark_report_2)

    #     post :create, params: {survey_id: survey.id, report_id: "admin", powerpoint_report: {demographic: "example_demographic"}}
    #     expect(Jobs::BuildPowerpointJob).to have_received(:create).with(hash_including(comparison_ids: [benchmark_summary_1.id]))
    #   end

    #   it "always uses the gender demographic and not the demographic supplied in the request params" do
    #     gender_demographic_question = FactoryBot.create(:demographic_question, name: "Gender Identity", type: :segment)
    #     gender_demographic_stq = FactoryBot.create(:survey_to_question, :with_theme, survey: survey, question: gender_demographic_question)

    #     other_demographic_question = FactoryBot.create(:demographic_question, name: "Geekiness", type: :segment)
    #     FactoryBot.create(:survey_to_question, :with_theme, survey: survey, question: other_demographic_question)

    #     post :create, params: {survey_id: survey.id, report_id: "admin", powerpoint_report: {demographic: "example_demographic"}}
    #     expect(Jobs::BuildPowerpointJob).to have_received(:create).with(hash_including(demographic_id: gender_demographic_stq.id))
    #   end
    # end
  end

  describe "#summaries" do
    let(:account) { FactoryBot.create(:account_with_master) }
    let(:survey) { FactoryBot.create(:basic_survey, account: account) }
    let(:admin) { FactoryBot.create(:admin, account: account) }

    let!(:current_summary) { FactoryBot.create(:summary, survey: survey, summary_type: :current_survey) }
    let!(:benchmark_summary) { FactoryBot.create(:summary, survey: survey, summary_type: :benchmark) }
    let!(:survey_summary) { FactoryBot.create(:summary, survey: survey, summary_type: :survey) }

    let(:context) { instance_double(Context, survey: survey, account: account, anchor: nil, anchors: [], filters: [], anchored_to_top_of_hierarchy?: false, full_reporting_line: false) }

    before do
      sign_in admin

      allow(controller).to receive(:survey).and_return(survey)
      allow(controller).to receive(:context).and_return(context)
      controller.instance_variable_set(:@context, context)
    end

    context "when user has access to multi-demographic report export" do
      before { set_mock_has_access_to_multi_demographic_report_exports(true) }

      it "calls new anchors method AND returns results including current and benchmark summaries" do
        expect_any_instance_of(Powerpoint::PowerpointAvailableComparisons)
          .to receive(:available_for_anchors)
          .with(context.anchors, context.anchored_to_top_of_hierarchy?, always_include_overall: true)
          .and_call_original

        expect_any_instance_of(Powerpoint::PowerpointAvailableComparisons)
          .not_to receive(:available_for_anchor_legacy)

        result = controller.send(:summaries)

        expect(result).to include(current_summary)
        expect(result).to include(benchmark_summary)
        expect(result.first.summary_type).to eq(:current_survey)
      end
    end

    context "when user does not have access to multi-demographic report export" do
      before { set_mock_has_access_to_multi_demographic_report_exports(false) }

      it "calls legacy anchor method AND returns results including current and benchmark summaries" do
        expect_any_instance_of(Powerpoint::PowerpointAvailableComparisons)
          .to receive(:available_for_anchor_legacy)
          .with(context.anchor, context.anchored_to_top_of_hierarchy?, always_include_overall: true)
          .and_call_original

        expect_any_instance_of(Powerpoint::PowerpointAvailableComparisons)
          .not_to receive(:available_for_anchors)

        result = controller.send(:summaries)

        expect(result).to include(current_summary)
        expect(result).to include(benchmark_summary)
        expect(result.first.summary_type).to eq(:current_survey)
      end
    end
  end
end
