class Powerpoint::PowerpointAvailableComparisons < ::AvailableComparisons
  def initialize(summaries, filters, overall_comparison_label: nil, overall_comparison_key: nil, full_reporting_line: false)
    @summaries = summaries
    @filters = filters
    @overall_comparison_label = overall_comparison_label
    @overall_comparison_key = overall_comparison_key
    @full_reporting_line = full_reporting_line
  end

  # To be depreciated
  def available_for_anchor_legacy(anchor, anchored_to_top_of_hierarchy, always_include_overall: false)
    comparisons = []
    is_top_level = top_level_report_legacy?(anchor, anchored_to_top_of_hierarchy)
    include_overall = !is_top_level || always_include_overall

    comparisons += is_top_level ? top_level_summaries : demographic_level_summaries
    comparisons += benchmarks
    comparisons = ComparisonSorter.sort(comparisons)
    comparisons.unshift(current_survey_overall) if include_overall
    comparisons
  end

  def available_for_anchors(anchors, anchored_to_top_of_hierarchy, always_include_overall: false)
    comparisons = []
    is_top_level = top_level_report?(anchors, anchored_to_top_of_hierarchy)
    include_overall = !is_top_level || always_include_overall

    comparisons += is_top_level ? top_level_summaries : demographic_level_summaries
    comparisons += benchmarks
    comparisons = ComparisonSorter.sort(comparisons)
    comparisons.unshift(current_survey_overall) if include_overall
    comparisons
  end

  private

  attr_reader :filters

  # To be depreciated
  def top_level_report_legacy?(anchor, anchored_to_top_of_hierarchy)
    return false if demographic_filters.present?

    anchor.nil? || anchor.is_a?(AllResultsReportFilter) || anchored_to_top_of_hierarchy
  end

  def top_level_report?(anchors, anchored_to_top_of_hierarchy)
    return false if demographic_filters.present?

    anchors.empty? ||
      anchors.first.is_a?(AllResultsReportFilter) ||
      anchored_to_top_of_hierarchy
  end

  def demographic_level_summaries
    summary_class = @full_reporting_line ? HierarchySummary : DemographicOptionSummary
    top_level_summaries.flat_map { |summary|
      demographic_filters.map do |filter|
        summary_class
          .where(overall_summary_id: summary.id,
                 select_option_id: filter.option.id.to_s,
                 survey_to_question_id: filter.survey_to_question.id.to_s)
          .order_by(created_at: :asc)
          .last
      end
    }.compact
  end

  def demographic_filters
    @demographic_filters ||= filters.reject { |f| f.is_a?(AllResultsReportFilter) }
  end
end
