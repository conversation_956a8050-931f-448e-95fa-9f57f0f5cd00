module SAML
  module Authenticator
    class << self
      def valid_saml_account?(account)
        return :inactive_saml unless account&.active_saml?
        return :saml_not_allowed unless account_authenticator(account).saml_allowed?

        :ok
      end

      def authenticate(account, saml_integration, response, logger: Rails.logger)
        return [:no_saml_response, nil] if response.blank?

        saml_response = build_saml_response(account, saml_integration, response)

        if saml_response.nil? || !saml_integration.validate(saml_response)
          logger.error "Failed SAML authentication for subdomain '#{account.subdomain}'. #{saml_response&.errors}"
          logger.error "response payload:"
          uuid = SecureRandom.uuid
          logger.error "BEGIN SAML BODY LOGGING"
          print_width = 1000
          lines_count = (response.length / print_width) + 1
          lines_count.times do |lineno|
            logger.error "saml_body subdomain=#{account.subdomain} identifier=#{uuid} index=#{lineno.to_s.rjust(4, "0")}/#{lines_count} text=#{response.slice(lineno * print_width, print_width)}"
          end
          logger.error "END SAML BODY LOGGING"
          return [:invalid_response, nil]
        end

        unless valid_name_id_format?(saml_integration, saml_response)
          logger.error "Failed SAML authentication for subdomain '#{account.subdomain}'. NameId format error."
          return [:invalid_name_id_format, nil]
        end

        saml_user = saml_integration.find_user(saml_response.name_id)

        is_valid_fa_user = is_valid_fusionauth_user(account, saml_response, saml_integration, saml_user)

        if saml_user.blank?
          logger.error "Failed SAML authentication for subdomain '#{account.subdomain}'. User with name_id #{saml_response.name_id} wasn't found for the subdomain."
          return [:invalid_user, nil]
        end

        logger.info "'#{saml_response.name_id}' authenticated for subdomain '#{account.subdomain}'."

        [:ok, saml_user, is_valid_fa_user]
      end

      private

      def account_authenticator(account)
        AccountProperties::Authenticator.for_account(account_id: account.id)
      end

      def is_valid_fusionauth_user(account, saml_response, saml_integration, saml_user)
        return false unless enable_fusionauth_sign_in?(account)

        fusionauth_sign_in_result = sign_in_to_fusionauth(account, saml_response.name_id, saml_integration.authentication_field)

        if fusionauth_sign_in_result.success? && saml_user.blank?
          raise Authentication::FusionAuthException.new("Failed SAML authentication for subdomain '#{account.subdomain}'. User with name_id #{saml_response.name_id} wasn't found for the subdomain.", 404)
        elsif !fusionauth_sign_in_result.success? && saml_user.present?
          raise Authentication::FusionAuthException.new("Failed Fusionauth authentication for subdomain '#{account.subdomain}'. Issue caused by #{fusionauth_sign_in_result.failure}. SAML_USER: #{saml_user.to_json}", 404)
        end

        fusionauth_sign_in_result.value![:is_valid_fusionauth_user] if fusionauth_sign_in_result.success?
      rescue => exception
        splunk_logger = Splunk::Logger.new(Rails.logger)
        splunk_logger.log(
          app: "murmur",
          module: "authentication.is_valid_fusionauth_user",
          code: "authentication.is_valid_fusionauth_user.error",
          msg: "Error verifying FusionAuth user",
          error: exception.to_s,
          error_msg: exception.message
        )

        false
      end

      def enable_fusionauth_sign_in?(account)
        FeatureFlags::Queries::ValueForAccount.new.call(
          account_aggregate_id: account&.aggregate_id,
          flag_name: FeatureFlags::Flags::FUSIONAUTH_SAML_ROLLOUT_FLAG,
          fallback_value: false
        )
      end

      def sign_in_to_fusionauth(account, name_id, authentication_field)
        Authentication::Commands::FusionAuthSamlSignIn.new(account: account).call(account: account, name_id: name_id, authentication_field: authentication_field)
      end

      def build_saml_response(account, saml_integration, response)
        OneLogin::RubySaml::Response.new(
          response,
          settings: settings_for_response(saml_integration),
          allowed_clock_drift: (
            account.config(Configs::ALLOWED_CLOCK_DRIFT_IN_SECONDS).seconds
          ),
          skip_destination: true,
          # In certain local tests of the SAML integration (against a configured OneLogin
          # account), the recipient check validation failed as part of the
          # `Response#validate_subject_confirmation` step. This validation step was
          # introduced _after_ the previous (forked) version of the gem we were using, so
          # for the time being, we're disabling this step, and will look to reenaable it
          # after further testing.
          skip_recipient_check: true
        )
      rescue REXML::ParseException
        nil
      end

      def settings_for_response(saml_integration)
        saml_integration.parse_remote
        SAML::Settings.new(saml_integration: saml_integration).for_response
      end

      def valid_name_id_format?(saml_integration, saml_response)
        # skip name_id_format validation if authentication is email
        # this protects existing integration from breaking if users misconfigured it
        return true if saml_integration.authentication_field == "email"

        # validate name_id_format against saml_integration config
        return true if saml_response.name_id_format == saml_integration.name_id_format

        false
      end
    end
  end
end
