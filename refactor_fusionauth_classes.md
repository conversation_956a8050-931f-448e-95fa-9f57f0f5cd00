# FusionAuth Client Refactoring Guide

## Overview
This guide shows how to refactor all classes that use `fusionauth_client: ::Authentication::FusionAuthClient.new` to support account-aware multi-tenant FusionAuth clients behind the feature flag.

## Pattern to Apply

### 1. Include the FusionAuthHelper module
```ruby
include Authentication::FusionAuthHelper
```

### 2. Update the initialize method
**Before:**
```ruby
def initialize(
  fusionauth_client: ::Authentication::FusionAuthClient.new,
  # other params...
)
  @fusionauth_client = fusionauth_client
  # other assignments...
end
```

**After:**
```ruby
def initialize(
  account: nil,
  fusionauth_client: nil,
  # other params...
)
  @fusionauth_client = fusionauth_client || build_fusionauth_client(account)
  # other assignments...
end
```

### 3. Update callers to pass account
When instantiating these classes, pass the account:
```ruby
# Before
SomeCommand.new.call(account: account, ...)

# After  
SomeCommand.new(account: account).call(account: account, ...)
```

## Classes to Update

### Commands
1. `Authentication::Commands::FusionAuthSamlSignIn` ✅ (DONE)
2. `Authentication::Commands::FusionAuthStartPasswordlessLogin`
3. `Authentication::Commands::FusionAuthCompletePasswordlessLogin`
4. `Authentication::Commands::BackfillSlidingWindowPolicyInFusionauth`
5. `Authentication::Commands::FusionAuthPasswordlessLogin`
6. `Authentication::Commands::FusionAuthCreateEntity` (already includes helper)
7. `Authentication::Commands::RegisterNewFusionAuthUser`
8. `Authentication::Commands::FusionAuthSearchEntityType`
9. `Authentication::Commands::ResetRegisteredApplicationsInGoogleIDP`
10. `Authentication::Commands::FusionAuthVendJWT`
11. `Authentication::Commands::FusionAuthSignOut`
12. `Authentication::Commands::ImportFusionAuthUser`

### Queries
1. `Authentication::Queries::FetchFusionAuthApplication`
2. `Authentication::Queries::FetchFusionAuthBlockingActions`
3. `Authentication::Queries::FetchFusionAuthUserByEmployeeId`
4. `Authentication::Queries::FetchFusionAuthUserByEmail`

### Special Cases
- `Authentication::Queries::FetchFusionAuthUsersFromAllTenants` - uses different client
- `Authentication::Commands::UpdateFusionAuthPassword` - uses different client
- `Authentication::Queries::FetchFusionAuthUser` - uses different client

## Helper Method Added to FusionAuthHelper

```ruby
def build_fusionauth_client(account)
  if account && subdomain_aware_login_enabled?(account.subdomain)
    multi_tenants_fusionauth_client(account.subdomain)
  else
    ::Authentication::FusionAuthClient.new
  end
end
```

## Testing Considerations

1. Update tests to pass `account` parameter when needed
2. Test both feature flag ON and OFF scenarios
3. Ensure backward compatibility when `account` is nil
4. Test that the correct client is used based on the feature flag

## Example Complete Refactor

See `Authentication::Commands::FusionAuthSamlSignIn` for the complete pattern.
