require "rails_helper"

RSpec.describe Powerpoint::PowerpointAvailableComparisons do
  let(:stq) { double(id: stq_id) }
  let(:stq_id) { BSON::ObjectId.new }
  let(:option) { double(id: option_id) }
  let(:other_option) { double(id: other_option_id) }
  let(:option_id) { BSON::ObjectId.new }
  let(:other_option_id) { BSON::ObjectId.new }

  let!(:benchmark_summary) { Summary.create!(summary_type: :benchmark) }
  let!(:survey_summary) { Summary.create!(summary_type: :survey) }
  let!(:current_survey_summary) { Summary.create!(summary_type: :current_survey) }
  let!(:demographic_summary) do
    DemographicOptionSummary.create!(
      overall_summary_id: survey_summary.id,
      survey_to_question_id: stq_id,
      select_option_id: option_id
    )
  end
  let!(:other_demographic_summary) do
    DemographicOptionSummary.create!(
      overall_summary_id: survey_summary.id,
      survey_to_question_id: stq_id,
      select_option_id: other_option_id
    )
  end
  let!(:hierarchy_summary) do
    HierarchySummary.create!(
      overall_summary_id: survey_summary.id,
      survey_to_question_id: stq_id,
      select_option_id: option_id
    )
  end
  let(:anchor) { instance_double(ReportFilter, option: option, survey_to_question: stq) }
  let(:anchor_2) { instance_double(ReportFilter, option: other_option, survey_to_question: stq) }
  let(:anchors) { [anchor, anchor_2] }
  let(:filters) { [anchor, anchor_2] }

  let(:summaries) do
    [
      benchmark_summary,
      survey_summary,
      demographic_summary,
      other_demographic_summary,
      current_survey_summary,
      hierarchy_summary
    ]
  end

  let(:full_reporting_line) { false }

  describe "legacy method: available_for_anchor_legacy" do
    subject(:anchor_comparisons) { Powerpoint::PowerpointAvailableComparisons.new(summaries, filters, full_reporting_line: full_reporting_line).available_for_anchor_legacy(anchor, anchored_to_top_of_hierarchy) }
    context "when a top level report" do
      context "when filters provided" do
        context "when demographic filters" do
          let(:anchor) { AllResultsReportFilter.new }
          let(:anchored_to_top_of_hierarchy) { true }

          it { is_expected.to include benchmark_summary }
          it { is_expected.not_to include survey_summary }
          it { is_expected.not_to include current_survey_summary }
          it { is_expected.to include other_demographic_summary }
          it { is_expected.not_to include hierarchy_summary }
        end
      end

      context "when only filter provided is an all results report filter" do
        let(:filters) { [AllResultsReportFilter.new] }
        let(:anchor) { nil }
        let(:anchored_to_top_of_hierarchy) { false }

        it { is_expected.to include benchmark_summary }
        it { is_expected.to include survey_summary }
        it { is_expected.to include current_survey_summary }
        it { is_expected.not_to include demographic_summary }
        it { is_expected.not_to include other_demographic_summary }
        it { is_expected.not_to include hierarchy_summary }
      end

      context "when no filters provided" do
        let(:filters) { [] }
        let(:anchor) { nil }
        let(:anchored_to_top_of_hierarchy) { false }

        it { is_expected.to include benchmark_summary }
        it { is_expected.to include survey_summary }
        it { is_expected.to include current_survey_summary }
        it { is_expected.not_to include demographic_summary }
        it { is_expected.not_to include other_demographic_summary }
        it { is_expected.not_to include hierarchy_summary }
      end
    end

    context "when the demographic summary is not passed in" do
      let(:anchored_to_top_of_hierarchy) { false }

      let(:summaries) do
        [
          benchmark_summary,
          survey_summary
        ]
      end

      it { is_expected.to include benchmark_summary }
      it { is_expected.not_to include survey_summary }
      it { is_expected.not_to include current_survey_summary }
      it { is_expected.to include demographic_summary }
      it { is_expected.to include other_demographic_summary }
      it { is_expected.not_to include hierarchy_summary }

      it "includes the overall summary first" do
        expect(anchor_comparisons.first.display_name).to eq "Company overall"
      end
    end

    context "when we are on a full reporting line hierarchy report" do
      let(:anchored_to_top_of_hierarchy) { false }
      let(:full_reporting_line) { true }

      it { is_expected.to include benchmark_summary }
      it { is_expected.not_to include survey_summary }
      it { is_expected.not_to include current_survey_summary }
      it { is_expected.not_to include demographic_summary }
      it { is_expected.not_to include other_demographic_summary }
      it { is_expected.to include hierarchy_summary }

      it "includes the overall summary first" do
        expect(anchor_comparisons.first.display_name).to eq "Company overall"
      end
    end
  end

  describe "new method: available_for_anchor" do
    subject(:anchors_comparisons) { Powerpoint::PowerpointAvailableComparisons.new(summaries, filters, full_reporting_line: full_reporting_line).available_for_anchors(anchors, anchored_to_top_of_hierarchy) }

    context "when no anchors and no filters are provided" do
      let(:anchors) { [] }
      let(:filters) { [] }

      let(:anchored_to_top_of_hierarchy) { false }

      it { is_expected.to include benchmark_summary }
      it { is_expected.to include survey_summary }
      it { is_expected.to include current_survey_summary }
      it { is_expected.not_to include demographic_summary }
      it { is_expected.not_to include other_demographic_summary }
      it { is_expected.not_to include hierarchy_summary }
    end

    context "when the anchors is points to all results and no filters are provided" do
      let(:anchors) { [AllResultsReportFilter.new] }
      let(:filters) { [] }

      let(:anchored_to_top_of_hierarchy) { false }

      it { is_expected.to include benchmark_summary }
      it { is_expected.to include survey_summary }
      it { is_expected.to include current_survey_summary }
      it { is_expected.not_to include demographic_summary }
      it { is_expected.not_to include other_demographic_summary }
      it { is_expected.not_to include hierarchy_summary }
    end

    context "when no anchors provided and filter points to All results report filter" do
      let(:anchors) { [] }
      let(:filters) { [AllResultsReportFilter.new] }

      let(:anchored_to_top_of_hierarchy) { false }

      it { is_expected.to include benchmark_summary }
      it { is_expected.to include survey_summary }
      it { is_expected.to include current_survey_summary }
      it { is_expected.not_to include demographic_summary }
      it { is_expected.not_to include other_demographic_summary }
      it { is_expected.not_to include hierarchy_summary }
    end

    context "when no anchors and a single filter is provided" do
      let(:anchors) { [] }
      let(:filters) { [anchor] }

      let(:anchored_to_top_of_hierarchy) { false }

      it { is_expected.to include benchmark_summary }
      it { is_expected.not_to include survey_summary }
      it { is_expected.not_to include current_survey_summary }
      it { is_expected.to include demographic_summary }
      it { is_expected.not_to include other_demographic_summary }
      it { is_expected.not_to include hierarchy_summary }
    end

    context "when a single anchor is provided" do
      let(:anchors) { [anchor] }
      let(:anchored_to_top_of_hierarchy) { false }

      it { is_expected.to include benchmark_summary }
      it { is_expected.not_to include survey_summary }
      it { is_expected.not_to include current_survey_summary }
      it { is_expected.to include demographic_summary }
      it { is_expected.to include other_demographic_summary }
      it { is_expected.not_to include hierarchy_summary }
    end

    context "when multiple anchors are provided" do
      let(:anchors) { [anchor, anchor_2] }
      let(:anchored_to_top_of_hierarchy) { false }

      it { is_expected.to include benchmark_summary }
      it { is_expected.not_to include survey_summary }
      it { is_expected.not_to include current_survey_summary }
      it { is_expected.to include demographic_summary }
      it { is_expected.to include other_demographic_summary }
      it { is_expected.not_to include hierarchy_summary }
    end

    context "when we are on a full reporting line hierarchy report" do
      let(:full_reporting_line) { true }
      let(:anchored_to_top_of_hierarchy) { false }

      it { is_expected.to include benchmark_summary }
      it { is_expected.not_to include survey_summary }
      it { is_expected.not_to include current_survey_summary }
      it { is_expected.not_to include demographic_summary }
      it { is_expected.not_to include other_demographic_summary }
      it { is_expected.to include hierarchy_summary }
    end
  end
end
