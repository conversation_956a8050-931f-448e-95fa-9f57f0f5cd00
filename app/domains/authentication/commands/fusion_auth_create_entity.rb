module Authentication
  module Commands
    class FusionAuthCreateEntity
      include Dry::Monads[:result]
      include Authentication::FusionAuthHelper

      LOGGER_APP = "murmur"
      LOGGER_MODULE = "authentication.commands.fusion_auth_create_entity"

      def initialize(
        fusionauth_client: ::Authentication::FusionAuthClient.new,
        splunk_logger: Splunk::Logger.new(Rails.logger)
      )
        @fusionauth_client = fusionauth_client
        @splunk_logger = splunk_logger
      end

      def call(entity_type_id:, session_id:, masquerade_effective_user_id:, real_user_id:, account_id:, masquerade_account_id:)
        account = Account.where(aggregate_id: account_id).first
        if account.nil?
          raise Authentication::FusionAuthException.new("Account not found", 404)
        end
        if subdomain_aware_login_enabled?(account.subdomain)
          @fusionauth_client = multi_tenants_fusionauth_client("cultureamp")
        end

        entity_request = build_request(entity_type_id, masquerade_effective_user_id, real_user_id, account_id, masquerade_account_id, session_id)
        response = create_entity(session_id, entity_request)

        Success(response)
      rescue => exception
        handle_error(exception, masquerade_effective_user_id)
      end

      private

      attr_reader :splunk_logger

      def build_request(entity_type_id, masquerade_effective_user_id, real_user_id, account_id, masquerade_account_id, session_id)
        {
          entity: {
            data: {
              masqueradeEndTime: 12.hours.from_now,
              masqueradeEffectiveUserId: masquerade_effective_user_id,
              masqueradeAccountId: masquerade_account_id,
              realUserId: real_user_id,
              realAccountId: account_id
            },
            type: {
              id: entity_type_id
            },
            name: generate_entity_name(session_id)
          }
        }
      end

      def generate_entity_name(session_id)
        "session #{session_id}"
      end

      def create_entity(entity_id, request)
        response = @fusionauth_client.create_entity(entity_id, request)

        validate_response!(response)
        response.success_response
      end

      def validate_response!(response)
        return if response.was_successful

        Rails.logger.error("Create Entity Error response: #{response.error_response}, status: #{response.status}")
        raise Authentication::FusionAuthException.new(response.error_response, response.status)
      end

      def handle_error(exception, masquerade_effective_user_id)
        log_error(exception, masquerade_effective_user_id)
        Failure(exception)
      end

      def log_error(exception, masquerade_effective_user_id)
        log_event(
          code: "#{LOGGER_MODULE}.error",
          msg: "Error creating entity in FusionAuth",
          error: exception.to_s,
          error_msg: exception.message,
          masquerade_effective_user_id: masquerade_effective_user_id
        )
        Sentry.capture_exception(exception)
      end

      def log_event(code:, msg:, **params)
        splunk_logger.log(
          app: LOGGER_APP,
          module: LOGGER_MODULE,
          code: code,
          msg: msg,
          **params
        )
      end
    end
  end
end
