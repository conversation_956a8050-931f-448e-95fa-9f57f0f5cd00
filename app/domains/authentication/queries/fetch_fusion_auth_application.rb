require "fusionauth/fusionauth_client"

module Authentication
  module Queries
    class FetchFusionAuthApplication
      include Dry::Monads[:result]
      include Authentication::FusionAuthHelper

      def initialize(account: nil, fusionauth_client: nil, splunk_logger: Splunk::Logger.new(Rails.logger))
        @fusionauth_client = fusionauth_client || build_fusionauth_client(account)
        @splunk_logger = splunk_logger
      end

      def call(application_id:)
        fetch_application(application_id)
      rescue => exception
        splunk_logger.log(
          app: "murmur",
          module: "authentication.queries.fetch_fusionauth_application.call",
          code: "authentication.queries.fetch_fusionauth_application.call.error",
          msg: "Error fetching application from FusionAuth by call method",
          error: exception.to_s,
          error_msg: exception.message,
          application_id: application_id
        )
        Sentry.capture_exception(exception)
        Failure(exception)
      end

      def call_with_account(account:)
        application_id = fusionauth_client.get_application_id(account)
        fetch_application(application_id)
      rescue => exception
        splunk_logger.log(
          app: "murmur",
          module: "authentication.queries.fetch_fusionauth_application.call_with_account",
          code: "authentication.queries.fetch_fusionauth_application.call_with_account.error",
          msg: "Error fetching application from FusionAuth by call_with_account method",
          error: exception.to_s,
          error_msg: exception.message,
          application_id: application_id
        )
        Sentry.capture_exception(exception)
        Failure(exception)
      end

      private

      attr_reader :fusionauth_client, :splunk_logger

      # doc: https://fusionauth.io/docs/v1/tech/apis/applications#retrieve-an-application
      def fetch_application(application_id)
        response = fusionauth_client.retrieve_application(application_id)

        unless response.was_successful
          raise Authentication::FusionAuthException.new(response.error_response, response.status)
        end

        Success(response.success_response.application)
      end
    end
  end
end
