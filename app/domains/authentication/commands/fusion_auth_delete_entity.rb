module Authentication
  module Commands
    class FusionAuthDeleteEntity
      include Dry::Monads[:result]
      include Authentication::FusionAuthHelper

      LOGGER_APP = "murmur"
      LOGGER_MODULE = "authentication.commands.fusion_auth_delete_entity"

      def initialize(
        fusionauth_client: ::Authentication::FusionAuthClient.new,
        splunk_logger: Splunk::Logger.new(Rails.logger)
      )
        @fusionauth_client = fusionauth_client
        @splunk_logger = splunk_logger
      end

      def call(entity_id:, account_id:)
        account = Account.where(aggregate_id: account_id).first
        if account.nil?
          raise Authentication::FusionAuthException.new("Account not found", 404)
        end
        if subdomain_aware_login_enabled?(account.subdomain)
          @fusionauth_client = multi_tenants_fusionauth_client("cultureamp")
        end

        response = delete_entity(entity_id)

        Success(response)
      rescue => exception
        handle_error(exception, entity_id)
      end

      private

      attr_reader :splunk_logger

      def delete_entity(entity_id)
        response = @fusionauth_client.delete_entity(entity_id)

        validate_response!(response)
        response.success_response
      end

      def validate_response!(response)
        return if response.was_successful
        return if response.status == 404

        Rails.logger.error("Delete Entity Error response: #{response.error_response}, status: #{response.status}")
        raise Authentication::FusionAuthException.new(response.error_response, response.status)
      end

      def handle_error(exception, entity_id)
        log_error(exception, entity_id)
        Failure(exception)
      end

      def log_error(exception, entity_id)
        log_event(
          code: "#{LOGGER_MODULE}.error",
          msg: "Error deleting entity in FusionAuth",
          error: exception.to_s,
          error_msg: exception.message,
          entity_id: entity_id
        )
        Sentry.capture_exception(exception)
      end

      def log_event(code:, msg:, **params)
        splunk_logger.log(
          app: LOGGER_APP,
          module: LOGGER_MODULE,
          code: code,
          msg: msg,
          **params
        )
      end
    end
  end
end
