require "rails_helper"

RSpec.describe SurveyDesign::MultiAccountSurveying::MapperUtils do
  shared_examples "a type mapper" do |method, input, expected_output, error_class, error_message|
    if expected_output
      it "maps #{input} to #{expected_output}" do
        expect(described_class.send(method, input)).to eq(expected_output)
      end
    else
      it "raises an error for #{input}" do
        expect {
          described_class.send(method, input)
        }.to raise_error(error_class, error_message)
      end
    end
  end

  describe ".question_intended_purpose_for" do
    include_examples "a type mapper",
      :question_intended_purpose_for,
      :segment,
      Domains::Enums::SurveyQuestionIntendedPurposes::DEMOGRAPHIC,
      nil,
      nil

    include_examples "a type mapper",
      :question_intended_purpose_for,
      :culture,
      Domains::Enums::SurveyQuestionIntendedPurposes::FEEDBACK,
      nil,
      nil

    include_examples "a type mapper",
      :question_intended_purpose_for,
      :unknown,
      nil,
      SurveyDesign::MultiAccountSurveying::UnknownQuestionTypeError,
      /Unable to find intended purpose for type: unknown/
  end

  describe ".section_intended_purpose_for" do
    include_examples "a type mapper",
      :section_intended_purpose_for,
      :segment,
      Domains::Enums::SectionIntendedPurposes::DEMOGRAPHIC,
      nil,
      nil

    include_examples "a type mapper",
      :section_intended_purpose_for,
      :culture,
      Domains::Enums::SectionIntendedPurposes::STANDARD,
      nil,
      nil

    include_examples "a type mapper",
      :section_intended_purpose_for,
      :unknown,
      nil,
      SurveyDesign::MultiAccountSurveying::UnknownSectionTypeError,
      /Unable to find intended purpose for type: unknown/
  end

  describe ".map_translations" do
    it "maps translations and skips entries with nil locale or text" do
      translations = {
        :en => "Hello",
        :fr => "Bonjour",
        :es => nil,
        nil => "Hallo",
        :de => "Hallo"
      }

      result = described_class.map_translations(translations)

      expect(result).to contain_exactly(
        {text: "Hello", locale: :en},
        {text: "Bonjour", locale: :fr},
        {text: "Hallo", locale: :de}
      )
    end
  end
end
