module Authentication
  module Commands
    class FusionAuthSamlSignIn
      include Dry::Monads[:result, :do]
      include Authentication::<PERSON><PERSON><PERSON><PERSON>el<PERSON>

      def initialize(
        account: nil,
        fusionauth_client: nil,
        fetch_by_email_query: Authentication::Queries::FetchFusionAuthUserByEmail.new,
        fetch_by_employee_id_query: Authentication::Queries::FetchFusionAuthUserByEmployeeId.new,
        fetch_blocking_actions_query: Authentication::Queries::FetchFusionAuthBlockingActions.new,
        fetch_application_query: Authentication::Queries::FetchFusionAuthApplication.new
      )
        @fusionauth_client = fusionauth_client || build_fusionauth_client(account)
        @fetch_by_email_query = fetch_by_email_query
        @fetch_by_employee_id_query = fetch_by_employee_id_query
        @fetch_blocking_actions_query = fetch_blocking_actions_query
        @fetch_application_query = fetch_application_query
      end

      def call(account:, name_id:, authentication_field:)
        fusionauth_user = yield fetch_user(name_id, authentication_field, account)
        user_registration = yield fetch_user_registration(fusionauth_user)

        # check if user is active
        if is_inactive_user?(user_registration)
          return Failure(:invalid_user)
        end

        # check if there are any blocking user actions for the user
        blocking_actions = yield fetch_blocking_actions(user_id: fusionauth_user.id)
        unless blocking_actions.empty?
          return Failure(:invalid_user)
        end

        # fetch the application for this users account
        fusionauth_application = yield fetch_application(user_registration)

        # check the user's account is the same as in fusionauth
        unless account.aggregate_id == fusionauth_application.data["aggregateId"]
          return Failure(:invalid_account)
        end

        Success(is_valid_fusionauth_user: true)
      end

      private

      attr_reader :fusionauth_client, :fetch_by_email_query, :fetch_application_query, :fetch_blocking_actions_query, :fetch_by_employee_id_query, :fusion_auth_start_passwordless_login, :fusion_auth_complete_passwordless_login

      def fetch_user(name_id, authentication_field, account)
        authentication_field.to_sym == :email ? fetch_user_by_email(name_id) : fetch_user_by_employee_id(name_id, account)
      end

      def fetch_user_by_email(name_id)
        fetch_by_email_query.call(email: name_id)
      end

      def fetch_user_by_employee_id(name_id, account)
        fetch_by_employee_id_query.call(employee_id: name_id, account: account)
      end

      def fetch_application(user_registration)
        # fetch the application that the user is registered to
        fetch_application_query.call(application_id: user_registration.applicationId)
      end

      def fetch_blocking_actions(user_id:)
        fetch_blocking_actions_query.call(user_id: user_id)
      end

      def start_passwordless_login(email, application_id)
        fusion_auth_start_passwordless_login.call(email: email, application_id: application_id)
      end

      def complete_passwordless_login(code)
        fusion_auth_complete_passwordless_login.call(code: code)
      end

      def fetch_user_registration(user)
        # At this point in time we only expect the user to be registered to one FusionAuth application.
        user_registration = user&.registrations&.first
        return Failure(:invalid_user_registration) unless user_registration

        Success(user_registration)
      end

      def is_inactive_user?(user_registration)
        if user_registration.data["startDate"].present?
          if Time.now.utc < user_registration.data["startDate"].to_time
            return true
          end
        end

        if user_registration.data["endDate"].present?
          if Time.now.utc > user_registration.data["endDate"].to_time
            return true
          end
        end

        false
      end
    end
  end
end
