module Authentication
  module Commands
    class FusionAuthPasswordlessLogin
      include Dry::Monads[:result, :do]
      include Authentication::<PERSON><PERSON><PERSON><PERSON>el<PERSON>

      def initialize(
        account: nil,
        fusionauth_client: nil,
        start_passwordless_login: nil,
        complete_passwordless_login: nil
      )
        @fusionauth_client = fusionauth_client || build_fusionauth_client(account)
        @start_passwordless_login = start_passwordless_login || ::Authentication::Commands::FusionAuthStartPasswordlessLogin.new(account: account)
        @complete_passwordless_login = complete_passwordless_login || ::Authentication::Commands::FusionAuthCompletePasswordlessLogin.new(account: account)
      end

      def call(account:, email:)
        application_id = fusionauth_client.get_application_id(account)

        code = yield _start_passwordless_login(email, application_id)
        complete_response = yield _complete_passwordless_login(code)

        Success(
          jwt: complete_response[:jwt],
          refresh_token: complete_response[:refresh_token]
        )
      end

      private

      attr_reader :fusionauth_client, :start_passwordless_login, :complete_passwordless_login

      def _start_passwordless_login(email, application_id)
        @start_passwordless_login.call(email: email, application_id: application_id)
      end

      def _complete_passwordless_login(code)
        @complete_passwordless_login.call(code: code)
      end
    end
  end
end
