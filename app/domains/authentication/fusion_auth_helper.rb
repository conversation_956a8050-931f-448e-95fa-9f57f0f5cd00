module Authentication
  module <PERSON><PERSON><PERSON><PERSON>el<PERSON>
    def fusionauth_jwt_post_signin_enabled?(subdomain)
      FeatureFlags::Queries::ValueForContext.new.call(
        flag_name: FeatureFlags::Flags::FUSIONAUTH_JWT_POST_SIGNIN,
        subdomain: subdomain,
        fallback_value: false
      )
    end

    def delete_fusionauth_entity(sid, account_id)
      delete_entity = Authentication::Commands::FusionAuthDeleteEntity.new
      result = delete_entity.call(entity_id: sid, account_id: account_id)

      unless result.success?
        Rails.logger.error("Failed to delete FusionAuth entity for masquerade session")
        Sentry.capture_exception(result.failure)
        raise "Failed to delete FusionAuth entity for masquerade session"
      end
    end

    def sign_out_fusionauth(user_id:, refresh_token:, global: false)
      sign_out_command = Authentication::Commands::FusionAuthSignOut.new
      result = sign_out_command.call(user_id: user_id, refresh_token: refresh_token, global: global)

      unless result.success?
        Rails.logger.error("Failed to sign out from FusionAuth")
        Sentry.capture_exception(result.failure)
        raise "Failed to sign out from FusionAuth"
      end
    end

    def fusionauth_passwordless_login(user)
      result = Authentication::Commands::FusionAuthPasswordlessLogin.new.call(
        account: user.account,
        email: user.email
      )

      if result.success?
        [result.value![:jwt], result.value![:refresh_token]]
      end
    end

    def get_subdomain_from_request
      return nil unless respond_to?(:request) && request.present?

      if respond_to?(:params) && params["fake-subdomain"].present? && !Rails.env.production?
        return params["fake-subdomain"]
      end

      if respond_to?(:params) && params["sd"].present? && !Rails.env.production?
        return params["sd"]
      end

      request.subdomains&.first
    end

    def subdomain_aware_login_enabled?(subdomain)
      FeatureFlags::Queries::ValueForContext.new.call(
        flag_name: FeatureFlags::Flags::SUBDOMAIN_AWARE_LOGIN_ENABLED,
        subdomain: subdomain,
        fallback_value: false
      )
    end

    def multi_tenants_fusionauth_client(subdomain)
      fusionauth_client = FusionAuth::FusionAuthClient.new(ENV["MURMUR_ALL_TENANTS_API_KEY"], ENV["FUSION_AUTH_HOST"])
      tenant_name = "#{ENV["FARM"]} - #{subdomain&.downcase}"
      request = {
        search: {
          name: tenant_name,
          startRow: 0,
          numberOfResults: 100,
          orderBy: "name ASC"
        }
      }

      response = fusionauth_client.search_tenants(request)
      unless response.was_successful
        raise Authentication::FusionAuthException.new(response.exception.message, response.status)
      end

      tenants = response.success_response.tenants
      if tenants.empty?
        error_message = "Tenant not found for: #{tenant_name}"
        raise Authentication::FusionAuthException.new(error_message, 404)
      end

      # Find the first tenant with exact name match (case-insensitive)
      exact_match_tenant = tenants.find { |tenant| tenant.name.downcase == tenant_name.downcase }

      if exact_match_tenant.nil?
        error_message = "No tenant found with exact name: #{tenant_name}"
        raise Authentication::FusionAuthException.new(error_message, 404)
      end

      tenant_id = exact_match_tenant.id
      fusionauth_client.set_tenant_id(tenant_id)

      fusionauth_client
    end

    def build_fusionauth_client(account)
      if account && subdomain_aware_login_enabled?(account.subdomain)
        multi_tenants_fusionauth_client(account.subdomain)
      else
        ::Authentication::FusionAuthClient.new
      end
    end
  end
end
