class PowerpointReportController < ReportsController
  component :report
  REPORT_TYPE = :manager_insight
  @model_id_name = :survey_id

  expose(:report_data) { Props::BuildPowerpointPageProps.new(summaries, demographic_stqs, paths, report_title, Appamp::Settings.email_support, context).to_hash }
  expose(:job) { Jobs::BuildPowerpointJob.find(job_id) }

  include ReportSharing::Queries::MultiDemographicFeatureFlag
  include MultiFormatReport
  include ParamsHelper

  def create
    return render_not_found if failed_reporting_threshold_checks?

    render_job_status(Jobs::BuildPowerpointJob.create(job_params))
  end

  def new
    render_report
  end

  def show
    render_job_status(job)
  end

  private

  def reporting_rules
    Reporting::Confidentiality::StandardRules.new.call(
      survey: survey
    )
  end

  def report_title
    return unless report_sharing?

    if report_access_grant.all_results_report?
      report_access_grant.report.name
    else
      "#{report_access_grant.report.name}: #{report_access_grant.select_option.label}"
    end
  end

  def job_params
    comparison_ids = [params["primary_comparison"], params["secondary_comparison"]]
    demographic_id = params.dig(:powerpoint_report, :demographic)

    if survey.account.free?
      comparison_ids = [latest_inclusion_benchmark_id]
      demographic_id = gender_demographic_id
    end

    job_params = {
      file_name: File.join(survey.account_id.to_s, survey.id.to_s, SecureRandom.uuid, "#{export_filename}.pptx"),
      comparison_ids: comparison_ids,
      demographic_id: demographic_id,
      anchor: anchor_question,
      leader_parameter: context.leader_parameter,
      overall_comparison_key: Context::COMPARISON_KEY_OVERALL,
      overall_comparison_label: context.survey.overall_label_config || I18n.t("common.benchmarks.company_overall"),
      locale: I18n.locale,
      action_link: action_link,
      full_reporting_line: full_reporting_line?,
      hierarchy_privacy: hierarchy_privacy?
    }.merge(basic_job_params)

    job_params[:demographic_filter_parameters] = context.filters(false).map(&:to_s)

    job_params
  end

  def action_link
    return unless survey.enabled?(Flags::LINK_TO_ACTION_FROM_PPT)
    return unless survey.powerpoint_template == :engagement
    return if report_id == "admin"

    path = ReportPaths.for(self).create_action_without_focus(utm_source: "ppt", utm_type: "action_link")
    account.account_url + path
  end

  def render_job_status(job)
    render json: job.completed? ? job_complete_params(job) : job_in_progress_params(job)
  end

  def job_in_progress_params(job)
    {
      status: job.status == :error ? "error" : "running",
      'refresh-url': paths.powerpoint_job(job.id),
      progress: 50
    }
  end

  def job_complete_params(job)
    {
      status: "done",
      'redirect-url': job.download_url
    }
  end

  def summaries
    all_summaries = Summary.overall_for_survey(survey)

    # exclude :survey when multiple filters applied
    if multiple_filters_applied?
      all_summaries = all_summaries.in(summary_type: [:current_survey, :benchmark])
    end

    comparisons = Powerpoint::PowerpointAvailableComparisons.new(
      all_summaries,
      context.filters,
      overall_comparison_label: survey.overall_label_config,
      overall_comparison_key: Context::COMPARISON_KEY_OVERALL,
      full_reporting_line: @context.full_reporting_line
    )

    can_export_mdr_report = has_access_to_multi_demographic_report_export?(
      user_id: current_user.aggregate_id,
      survey_id: survey.aggregate_id,
      account_id: current_user.account.aggregate_id
    )

    if can_export_mdr_report
      comparisons.available_for_anchors(context.anchors, context.anchored_to_top_of_hierarchy?, always_include_overall: true)
    else
      comparisons.available_for_anchor_legacy(context.anchor, context.anchored_to_top_of_hierarchy?, always_include_overall: true)
    end
  end

  def gender_demographic_id
    demographic_stqs.detect { |stq| stq.name == "Gender Identity" }&.id
  end

  def latest_inclusion_benchmark_id
    report = BenchmarkReport.where(type: :inclusion).order_by(created_at: :desc).first
    survey.summaries.active.find_by(benchmark_report: report)&.id if report
  end

  def multiple_filters_applied?
    context.filters(true).count { |f| !f.is_a?(AllResultsReportFilter) } > 1
  end

  def demographic_stqs
    context.dissect_questions
  end

  def establish_legacy_report_scope
    old_manager_insight_scope(ManagerInsightReportPermission, :insight_report_id)
  end

  def job_id
    params[:id] || params[:powerpoint_report_id]
  end

  def anchor_question
    return nil if context.anchor.nil? || context.anchor.is_a?(AllResultsReportFilter)

    {stq_id: context.anchor.survey_to_question.id.to_s, option_id: context.anchor.option.id.to_s}
  end

  def calculate_etag
    true
  end

  def failed_reporting_threshold_checks?
    return true if participation_result.error?

    participation_result.data.filtered_result.submission_count < reporting_rules.significance || filter_significance_result.contains_insignificant_filters?
  end
end
