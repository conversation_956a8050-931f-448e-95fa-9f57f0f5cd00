module Reports
  module InsightReport
    module BulkExport
      module PowerPoint
        class ComparisonOptions
          include ReportSharing::Queries::MultiDemographicFeatureFlag

          def initialize(
            powerpoint_comparison_options: Reports::InsightReport::Export::PowerPoint::ComparisonOptions.new,
            comparisons: ->(survey:) do
              all_summaries = Summary.overall_for_survey(survey).in(summary_type: [:current_survey, :benchmark])
              comparisons = Powerpoint::PowerpointAvailableComparisons.new(all_summaries, [], overall_comparison_key: Context::COMPARISON_KEY_OVERALL)

              can_export_mdr_report = has_access_to_multi_demographic_report_export?(
                user_id: nil,
                survey_id: survey.aggregate_id,
                account_id: survey.account.aggregate_id
              )
              if can_export_mdr_report
                comparisons.available_for_anchors([], false)
              else
                comparisons.available_for_anchor_legacy(nil, false)
              end
            end
          )
            @powerpoint_comparison_options = powerpoint_comparison_options
            @comparisons = comparisons
          end

          def call(survey:)
            comparisons = @comparisons.call(survey: survey)
            return legacy_comparison_options(comparisons: comparisons) unless survey.automatic_comparisons_v2_enabled?

            # we can't filter for compatible hierarchical comparison because
            # we don't have enough information regarding shared report
            @powerpoint_comparison_options.call(
              survey_id: survey.id.to_s,
              summaries: legacy_comparison_options(comparisons: comparisons)
            )
          end

          private

          def legacy_comparison_options(comparisons:)
            comparisons.map do |summary|
              Reports::InsightReport::Export::PowerPoint::ComparisonOption.new(
                id: summary.id.to_s,
                overall_summary_id: summary.id.to_s,
                name: summary.display_name,
                type: summary.summary_type
              )
            end
          end
        end
      end
    end
  end
end
