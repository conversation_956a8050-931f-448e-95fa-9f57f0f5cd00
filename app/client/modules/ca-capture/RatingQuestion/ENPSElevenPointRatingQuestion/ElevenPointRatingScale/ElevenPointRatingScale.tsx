import { FormattedMessage } from '@cultureamp/i18n-react-intl';
import { ColorSchema, InlineNotification, Text } from '@kaizen/components';
import React, { useState } from 'react';
import {
  DefaultEnpsElevenPointScaleRating,
  EnpsElevenPointScaleRating,
} from '../types';
import { ElevenPointRatingScaleItem } from './ElevenPointRatingScaleItem';

type ElevenPointRatingScaleProps = {
  colorSchema: ColorSchema;
  question: string;
  onChange: (rating: EnpsElevenPointScaleRating) => void;
  currentRating: DefaultEnpsElevenPointScaleRating;
  scaleLabels: {
    scaleStartLabel: string;
    scaleEndLabel: string;
  };
  hasValidationError: boolean;
  validationMessage: string;
  questionMandatory?: boolean;
};
export const ratingNotSelected = -1;

export const ElevenPointRatingScale = ({
  colorSchema,
  question,
  onChange,
  scaleLabels,
  currentRating,
  hasValidationError,
  validationMessage,
  questionMandatory,
}: ElevenPointRatingScaleProps) => {
  const [selectedRating, setSelectedRating] = useState<
    DefaultEnpsElevenPointScaleRating | EnpsElevenPointScaleRating
  >(currentRating);

  const handleItemSelect = (rating: EnpsElevenPointScaleRating) => {
    const currentRatingWasClicked = currentRating === rating;
    const newRating = currentRatingWasClicked ? ratingNotSelected : rating;
    onChange(newRating);
    setSelectedRating(newRating);
  };

  const scale: Exclude<EnpsElevenPointScaleRating, -1>[] = [
    0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10,
  ];

  return (
    <>
      <fieldset
        className="flex max-sm:flex-wrap max-sm:max-w-[16.125rem] gap-x-4 max-sm:gap-x-6 max-sm:gap-y-8 md:gap-y-0 mx-auto md:!m-0 max-sm:!mb-6 !mb-0 max-sm:justify-center max-sm:order-2 order-1"
        aria-required={questionMandatory ? 'true' : undefined}
        data-testid="enps-eleven-point-rating-scale"
      >
        <legend className="sr-only">{question}</legend>
        {scale.map((rating) => {
          const isFirstItem = rating === 0;
          const isLastItem = rating === 10;
          const isSelectedItem = selectedRating === rating;
          return (
            <ElevenPointRatingScaleItem
              rating={rating}
              isFirstItem={isFirstItem}
              isLastItem={isLastItem}
              isSelectedItem={isSelectedItem}
              colorSchema={colorSchema}
              onItemSelect={handleItemSelect}
            />
          );
        })}
      </fieldset>
      <div
        aria-hidden="true"
        className="flex justify-between w-full max-sm:order-1 order-2 max-sm:mt-16 mt-6"
      >
        <Text classNameOverride="text-gray-600" color="dark" variant="body">
          <span className="hidden max-sm:inline max-sm:overflow-auto">0=</span>
          {scaleLabels.scaleStartLabel}
        </Text>
        <Text classNameOverride="text-gray-600" color="dark" variant="body">
          <span className="hidden max-sm:inline max-sm:overflow-auto">10=</span>
          {scaleLabels.scaleEndLabel}
        </Text>
      </div>
      {hasValidationError && (
        <InlineNotification
          classNameOverride="!border-transparent !mt-8 !py-10"
          persistent
          variant="warning"
        >
          {validationMessage}
        </InlineNotification>
      )}
    </>
  );
};
