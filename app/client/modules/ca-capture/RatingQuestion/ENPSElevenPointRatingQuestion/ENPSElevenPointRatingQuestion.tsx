import React, { useState, useEffect, useRef } from 'react';
import csrfToken from 'ca-environment/csrfToken';
import request from 'ca-request';
import notifySavingError from 'ca-capture/helpers/notifySavingError';
import RatingActions from '../RatingActions/RatingActions';
import styles from '../RatingQuestion.scss';
import Submission from '../../Submission/Submission';
import { isValidENPSRating } from './helpers';
import {
  DefaultEnpsElevenPointScaleRating,
  EnpsElevenPointScaleRating,
} from 'ca-capture/RatingQuestion/ENPSElevenPointRatingQuestion/types';
import {
  ElevenPointRatingScale,
  ratingNotSelected,
} from './ElevenPointRatingScale';

type ENPSElevenPointRatingQuestionProps = {
  questionId: string;
  questionText: string;
  questionMandatory: boolean;
  defaultCommentContent: string;
  saveUrl: string;
  localeDirection: 'ltr' | 'rtl';
  allowComments: boolean;
  existingAnswer: DefaultEnpsElevenPointScaleRating;
  showAdministratorCanReplyToCommentMessage: boolean;
  scaleColorSchema: 'classical' | 'blue';
  uiText: {
    scaleStartLabel: string;
    scaleEndLabel: string;
    addComment: string;
    editComment: string;
    addCommentAria: string;
    editCommentAria: string;
    editCommentAriaDescription: string;
    dialogHeader: string;
    submitLabel: string;
    submittingLabel: string;
    dismissLabel: string;
    repliesInfo: string;
    savingError: string;
    mandatoryError: string;
    dismissWarningTitle: string;
    dismissWarningBody: string;
    dismissWarningConfirm: string;
    dismissWarningCancel: string;
  };
};

export const ENPSElevenPointRatingQuestion = (
  props: ENPSElevenPointRatingQuestionProps
) => {
  const {
    questionId,
    questionText,
    questionMandatory,
    defaultCommentContent,
    saveUrl,
    localeDirection,
    allowComments,
    uiText,
    existingAnswer,
    showAdministratorCanReplyToCommentMessage,
    scaleColorSchema,
  } = props;
  const questionHeaderId = `question-header-${questionId}`;
  const questionLabel = questionMandatory ? `${questionText} *` : questionText;
  const [selectedRating, setSelectedRating] =
    useState<DefaultEnpsElevenPointScaleRating>(existingAnswer);
  const containerRef = useRef<HTMLDivElement>(null);
  const [hasValidationError, setValidationError] = useState<boolean>(false);
  const noRatingProvidedRequestValue = '';

  // Without having a React container around the Capture form yet, we're listening for a custom event
  // sent from app/assets/javascripts/response.js to signal that submit has been pressed and we want to show validation errors.
  useEffect(() => {
    const checkAndSetError = (_event: any, questionIds: string[]) => {
      const answered = questionIds.indexOf(questionId) === ratingNotSelected;
      setValidationError(!answered);
    };
    if (questionMandatory) {
      $(document).on('promptUnanswered', checkAndSetError);
    }
    return () => {
      $(document).off('promptUnanswered', checkAndSetError);
    };
  }, [questionId, questionMandatory]);

  const getRatingRequestValue = (rating: EnpsElevenPointScaleRating) => {
    const ratingUnselected = rating === ratingNotSelected;
    const ratingNotValid = !isValidENPSRating(rating);
    if (ratingUnselected || ratingNotValid) {
      return noRatingProvidedRequestValue;
    }
    return rating;
  };

  const onRatingSelect = (rating: EnpsElevenPointScaleRating) => {
    const oldSelectedRating = selectedRating;
    setSelectedRating(rating);

    const ratingRequestValue = getRatingRequestValue(rating);

    if (ratingRequestValue !== noRatingProvidedRequestValue) {
      setValidationError(false);
    }

    const data = {
      authenticity_token: csrfToken(),
      answer: ratingRequestValue,
    };

    Submission.enqueue(() => {
      return request
        .put(saveUrl, {
          body: JSON.stringify(data),
        })
        .catch((err) => {
          notifySavingError(
            err,
            saveUrl,
            'ENPS Eleven Point Rating Question answer request',
            uiText.savingError
          );

          // Reset state
          setSelectedRating(oldSelectedRating);
        });
    });

    // Trigger jQuery for some adjustments to the .question container that we can't make here yet
    $(document).trigger('reactRatingSave', {
      container: containerRef.current,
      value: ratingRequestValue,
    });
  };

  return (
    <div className={`${styles.container} p-32`} ref={containerRef}>
      <div className={styles.flexContainer}>
        <div className={styles.label}>
          <p id={questionHeaderId}>{questionLabel}</p>
        </div>

        <div
          className={
            hasValidationError
              ? styles.scaleValidationError
              : `${styles.scale} flex flex-col`
          }
        >
          <ElevenPointRatingScale
            colorSchema={scaleColorSchema}
            question={questionText}
            onChange={onRatingSelect}
            currentRating={selectedRating}
            scaleLabels={{
              scaleStartLabel: uiText.scaleStartLabel,
              scaleEndLabel: uiText.scaleEndLabel,
            }}
            hasValidationError={hasValidationError}
            validationMessage={uiText.mandatoryError}
            questionMandatory={questionMandatory}
          />
        </div>

        <div className={styles.actions}>
          {allowComments && (
            <RatingActions
              questionId={questionId}
              questionText={questionText}
              defaultCommentContent={defaultCommentContent}
              saveUrl={saveUrl}
              localeDirection={localeDirection}
              uiText={uiText}
              showAdministratorCanReplyToCommentMessage={
                showAdministratorCanReplyToCommentMessage
              }
            />
          )}
        </div>
      </div>
    </div>
  );
};
