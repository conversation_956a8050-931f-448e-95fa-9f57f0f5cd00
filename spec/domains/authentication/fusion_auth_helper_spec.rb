require "rails_helper"
require "dry/monads"
require "fusionauth/fusionauth_client"

RSpec.describe Authentication::FusionAuthHelper, type: :helper do
  include Dry::Monads[:result]

  let(:dummy_class) { Class.new { include Authentication::FusionAuthHelper } }
  subject { dummy_class.new }

  describe "#fusionauth_jwt_post_signin_enabled?" do
    let(:subdomain) { "test-subdomain" }
    let(:feature_query) { instance_double("FeatureFlags::Queries::ValueForContext") }

    before do
      expect(FeatureFlags::Queries::ValueForContext).to receive(:new).and_return(feature_query)
    end

    it "calls the feature flag query with the correct parameters" do
      expect(feature_query).to receive(:call).with(
        flag_name: FeatureFlags::Flags::FUSIONAUTH_JWT_POST_SIGNIN,
        subdomain: "test-subdomain",
        fallback_value: false
      ).and_return(true)

      result = subject.fusionauth_jwt_post_signin_enabled?(subdomain)
      expect(result).to be true
    end

    it "returns false when the feature flag is disabled" do
      expect(feature_query).to receive(:call).with(
        flag_name: FeatureFlags::Flags::FUSIONAUTH_JWT_POST_SIGNIN,
        subdomain: "test-subdomain",
        fallback_value: false
      ).and_return(false)

      result = subject.fusionauth_jwt_post_signin_enabled?(subdomain)
      expect(result).to be false
    end
  end

  describe "#subdomain_aware_login_enabled?" do
    let(:subdomain) { "test-subdomain" }
    let(:feature_query) { instance_double("FeatureFlags::Queries::ValueForContext") }

    before do
      expect(FeatureFlags::Queries::ValueForContext).to receive(:new).and_return(feature_query)
    end

    it "calls the feature flag query with the correct parameters" do
      expect(feature_query).to receive(:call).with(
        flag_name: FeatureFlags::Flags::SUBDOMAIN_AWARE_LOGIN_ENABLED,
        subdomain: subdomain,
        fallback_value: false
      ).and_return(true)

      result = subject.subdomain_aware_login_enabled?(subdomain)
      expect(result).to be true
    end

    it "returns false when the feature flag is disabled" do
      expect(feature_query).to receive(:call).with(
        flag_name: FeatureFlags::Flags::SUBDOMAIN_AWARE_LOGIN_ENABLED,
        subdomain: subdomain,
        fallback_value: false
      ).and_return(false)

      result = subject.subdomain_aware_login_enabled?(subdomain)
      expect(result).to be false
    end
  end

  describe "#delete_fusionauth_entity" do
    let(:delete_entity) { instance_double("Authentication::Commands::FusionAuthDeleteEntity") }
    let(:success_result) { instance_double("Result", success?: true) }
    let(:failure_result) { instance_double("Result", success?: false, failure: StandardError.new("Test error")) }
    let(:sid) { "test-jwt-sid" }
    let(:account_id) { "test-account-id" }

    before do
      expect(Authentication::Commands::FusionAuthDeleteEntity).to receive(:new).and_return(delete_entity)
    end

    context "when deletion is successful" do
      it "calls the delete entity command with the jwt_sid" do
        expect(delete_entity).to receive(:call).with(entity_id: sid, account_id: account_id).and_return(success_result)

        expect { subject.delete_fusionauth_entity(sid, account_id) }.not_to raise_error
      end
    end

    context "when deletion fails" do
      before do
        expect(delete_entity).to receive(:call).with(entity_id: sid, account_id: account_id).and_return(failure_result)
      end

      it "logs the error" do
        expect(Rails.logger).to receive(:error).with("Failed to delete FusionAuth entity for masquerade session")
        expect(Sentry).to receive(:capture_exception).with(failure_result.failure)

        expect { subject.delete_fusionauth_entity(sid, account_id) }.to raise_error(RuntimeError, "Failed to delete FusionAuth entity for masquerade session")
      end

      it "sends the exception to Sentry" do
        expect(Rails.logger).to receive(:error).with("Failed to delete FusionAuth entity for masquerade session")
        expect(Sentry).to receive(:capture_exception).with(failure_result.failure)

        expect { subject.delete_fusionauth_entity(sid, account_id) }.to raise_error(RuntimeError)
      end

      it "raises an error" do
        expect(Rails.logger).to receive(:error).with("Failed to delete FusionAuth entity for masquerade session")
        expect(Sentry).to receive(:capture_exception).with(failure_result.failure)

        expect { subject.delete_fusionauth_entity(sid, account_id) }.to raise_error(RuntimeError, "Failed to delete FusionAuth entity for masquerade session")
      end
    end
  end

  describe "#multi_tenants_fusionauth_client" do
    let(:fusionauth_client) do
      double(
        FusionAuth::FusionAuthClient,
        search_tenants: search_tenants_response,
        set_tenant_id: true
      )
    end
    let(:subdomain) { "test-subdmain" }

    before do
      ENV["FARM"] = "test-farm"
      expect(FusionAuth::FusionAuthClient).to receive(:new).and_return(fusionauth_client)
    end

    context "when the tenant search is failed" do
      let(:search_tenants_response) do
        double(
          FusionAuth::ClientResponse,
          was_successful: false,
          exception: StandardError.new("Tenant search failed"),
          status: 500
        )
      end

      it "raise error" do
        expect(fusionauth_client).to receive(:search_tenants).and_return(search_tenants_response)
        expect(fusionauth_client).not_to receive(:set_tenant_id)
        expect { subject.multi_tenants_fusionauth_client(subdomain) }.to raise_error(Authentication::FusionAuthException, "Tenant search failed")
      end
    end

    context "when the tenant search is successful" do
      let(:search_tenants_response) do
        double(
          FusionAuth::ClientResponse,
          was_successful: true,
          success_response: response
        )
      end
      let(:response) { OpenStruct.new(tenants: tenants) }

      context "when found only one tenant with exact name match" do
        let(:tenant_id) { "tenant-123" }
        let(:tenants) do
          [
            OpenStruct.new(id: tenant_id, name: "test-farm - #{subdomain}")
          ]
        end

        it "return the fusioauth client with the correct tenant id" do
          expect(fusionauth_client).to receive(:search_tenants).and_return(search_tenants_response)
          expect(fusionauth_client).to receive(:set_tenant_id).with(tenant_id)
          expect { subject.multi_tenants_fusionauth_client(subdomain) }.not_to raise_error
        end
      end

      context "when search returns tenants but no exact name match" do
        let(:tenants) do
          [
            OpenStruct.new(id: "tenant-123", name: "test-farm - other"),
            OpenStruct.new(id: "tenant-456", name: "different-farm - #{subdomain}")
          ]
        end

        it "raise error" do
          expect(fusionauth_client).to receive(:search_tenants).and_return(search_tenants_response)
          expect(fusionauth_client).not_to receive(:set_tenant_id)
          expect { subject.multi_tenants_fusionauth_client(subdomain) }.to raise_error(Authentication::FusionAuthException, "No tenant found with exact name: test-farm - #{subdomain}")
        end
      end

      context "when not found tenant" do
        let(:tenants) { [] }

        it "raise error" do
          expect(fusionauth_client).to receive(:search_tenants).and_return(search_tenants_response)
          expect(fusionauth_client).not_to receive(:set_tenant_id)
          expect { subject.multi_tenants_fusionauth_client(subdomain) }.to raise_error(Authentication::FusionAuthException, "Tenant not found for: test-farm - #{subdomain}")
        end
      end

      context "when found multiple tenants with exact name match" do
        let(:tenants) do
          [
            OpenStruct.new(id: "tenant-123", name: "test-farm - #{subdomain}"),
            OpenStruct.new(id: "tenant-456", name: "test-farm - #{subdomain}")
          ]
        end

        it "return the fusionauth client with the first matching tenant id" do
          expect(fusionauth_client).to receive(:search_tenants).and_return(search_tenants_response)
          expect(fusionauth_client).to receive(:set_tenant_id).with("tenant-123")
          expect { subject.multi_tenants_fusionauth_client(subdomain) }.not_to raise_error
        end
      end

      context "when search returns multiple tenants with only one exact match" do
        let(:tenant_id) { "tenant-123" }
        let(:tenants) do
          [
            OpenStruct.new(id: tenant_id, name: "test-farm - #{subdomain}"),
            OpenStruct.new(id: "tenant-456", name: "test-farm - #{subdomain}-staging"),
            OpenStruct.new(id: "tenant-789", name: "other-farm - #{subdomain}")
          ]
        end

        it "return the fusionauth client with the correct tenant id" do
          expect(fusionauth_client).to receive(:search_tenants).and_return(search_tenants_response)
          expect(fusionauth_client).to receive(:set_tenant_id).with(tenant_id)
          expect { subject.multi_tenants_fusionauth_client(subdomain) }.not_to raise_error
        end
      end

      context "when found tenant with case-insensitive name match" do
        let(:tenant_id) { "tenant-123" }
        let(:tenants) do
          [
            OpenStruct.new(id: tenant_id, name: "TEST-FARM - #{subdomain.upcase}")
          ]
        end

        it "return the fusionauth client with the correct tenant id for uppercase tenant name" do
          expect(fusionauth_client).to receive(:search_tenants).and_return(search_tenants_response)
          expect(fusionauth_client).to receive(:set_tenant_id).with(tenant_id)
          expect { subject.multi_tenants_fusionauth_client(subdomain) }.not_to raise_error
        end

        it "return the fusionauth client with the correct tenant id for lowercase tenant name" do
          tenants.first.name = "test-farm - #{subdomain}"

          expect(fusionauth_client).to receive(:search_tenants).and_return(search_tenants_response)
          expect(fusionauth_client).to receive(:set_tenant_id).with(tenant_id)
          expect { subject.multi_tenants_fusionauth_client(subdomain) }.not_to raise_error
        end

        it "return the fusionauth client with the correct tenant id for mixed case tenant name" do
          tenants.first.name = "TeSt-FaRm - #{subdomain}"

          expect(fusionauth_client).to receive(:search_tenants).and_return(search_tenants_response)
          expect(fusionauth_client).to receive(:set_tenant_id).with(tenant_id)
          expect { subject.multi_tenants_fusionauth_client(subdomain) }.not_to raise_error
        end
      end
    end
  end

  describe "#sign_out_fusionauth" do
    let(:sign_out_command) { instance_double(Authentication::Commands::FusionAuthSignOut) }
    let(:result) { Success("signed out") }
    let(:user_id) { "user-123" }
    let(:refresh_token) { "token-123" }

    before do
      allow(Authentication::Commands::FusionAuthSignOut).to receive(:new).and_return(sign_out_command)
      allow(sign_out_command).to receive(:call).and_return(result)
    end

    context "when sign out is successful" do
      it "calls the sign out command with correct parameters" do
        expect(sign_out_command).to receive(:call).with(
          user_id: user_id,
          refresh_token: refresh_token,
          global: false
        )

        subject.sign_out_fusionauth(user_id: user_id, refresh_token: refresh_token)
      end
    end

    context "when sign out fails" do
      let(:result) { Failure("Sign out failed") }

      it "logs error and raises exception" do
        expect(Rails.logger).to receive(:error).with("Failed to sign out from FusionAuth")
        expect(Sentry).to receive(:capture_exception).with("Sign out failed")

        expect {
          subject.sign_out_fusionauth(user_id: user_id, refresh_token: refresh_token)
        }.to raise_error("Failed to sign out from FusionAuth")
      end
    end
  end

  describe "#get_subdomain_from_request" do
    let(:request) { instance_double("ActionDispatch::Request") }
    let(:params) { {} }

    before do
      allow(subject).to receive(:respond_to?).and_return(true)
      allow(subject).to receive(:request).and_return(request)
      allow(subject).to receive(:params).and_return(params)
      allow(request).to receive(:present?).and_return(true)
      allow(request).to receive(:subdomains).and_return([])
    end

    context "when request is not available" do
      it "returns nil when request method is not available" do
        allow(subject).to receive(:respond_to?).with(:request).and_return(false)
        allow(subject).to receive(:respond_to?).with(:params).and_return(true)

        result = subject.get_subdomain_from_request

        expect(result).to be_nil
      end

      it "returns nil when request is nil" do
        allow(subject).to receive(:request).and_return(nil)

        result = subject.get_subdomain_from_request

        expect(result).to be_nil
      end

      it "returns nil when request is not present" do
        allow(request).to receive(:present?).and_return(false)

        result = subject.get_subdomain_from_request

        expect(result).to be_nil
      end
    end

    context "when fake-subdomain parameter is provided" do
      let(:params) { {"fake-subdomain" => "test-subdomain"} }

      before do
        allow(Rails.env).to receive(:production?).and_return(false)
      end

      it "returns the fake subdomain in non-production environments" do
        result = subject.get_subdomain_from_request

        expect(result).to eq("test-subdomain")
      end

      it "ignores fake subdomain in production environment" do
        allow(Rails.env).to receive(:production?).and_return(true)
        allow(request).to receive(:subdomains).and_return(["real-subdomain"])

        result = subject.get_subdomain_from_request

        expect(result).to eq("real-subdomain")
      end

      it "returns nil when params method is not available" do
        allow(subject).to receive(:respond_to?).with(:request).and_return(true)
        allow(subject).to receive(:respond_to?).with(:params).and_return(false)
        allow(request).to receive(:subdomains).and_return(nil)

        result = subject.get_subdomain_from_request

        expect(result).to be_nil
      end
    end

    context "when sd parameter is provided" do
      let(:params) { {"sd" => "test-subdomain"} }

      before do
        allow(Rails.env).to receive(:production?).and_return(false)
      end

      it "returns the sd subdomain in non-production environments" do
        result = subject.get_subdomain_from_request

        expect(result).to eq("test-subdomain")
      end

      it "ignores sd parameter in production environment" do
        allow(Rails.env).to receive(:production?).and_return(true)
        allow(request).to receive(:subdomains).and_return(["real-subdomain"])

        result = subject.get_subdomain_from_request

        expect(result).to eq("real-subdomain")
      end

      it "returns nil when params method is not available" do
        allow(subject).to receive(:respond_to?).with(:request).and_return(true)
        allow(subject).to receive(:respond_to?).with(:params).and_return(false)
        allow(request).to receive(:subdomains).and_return(nil)

        result = subject.get_subdomain_from_request

        expect(result).to be_nil
      end
    end

    context "when both fake-subdomain and sd parameters are provided" do
      let(:params) { {"fake-subdomain" => "fake-subdomain-value", "sd" => "sd-value"} }

      before do
        allow(Rails.env).to receive(:production?).and_return(false)
      end

      it "prioritizes fake-subdomain over sd parameter" do
        result = subject.get_subdomain_from_request

        expect(result).to eq("fake-subdomain-value")
      end
    end

    context "when falling back to subdomain extraction from request" do
      it "returns the first subdomain" do
        allow(request).to receive(:subdomains).and_return(["test", "staging"])

        result = subject.get_subdomain_from_request

        expect(result).to eq("test")
      end

      it "returns the only subdomain when there's just one" do
        allow(request).to receive(:subdomains).and_return(["production"])

        result = subject.get_subdomain_from_request

        expect(result).to eq("production")
      end

      it "returns nil when subdomains array is empty" do
        allow(request).to receive(:subdomains).and_return([])

        result = subject.get_subdomain_from_request

        expect(result).to be_nil
      end

      it "returns nil when request subdomains is nil" do
        allow(request).to receive(:subdomains).and_return(nil)

        result = subject.get_subdomain_from_request

        expect(result).to be_nil
      end
    end
  end
end
